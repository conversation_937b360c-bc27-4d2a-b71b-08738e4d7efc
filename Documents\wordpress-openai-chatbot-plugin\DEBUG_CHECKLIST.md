# OpenAI Chatbot Plugin - Step-by-Step Debugging Checklist

## STEP 1: Enable Debug Mode

Add these lines to your `wp-config.php` file (before the "That's all, stop editing!" line):

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

Save the file and refresh your admin page.

## STEP 2: Check Debug Information Panel

1. Go to **Settings > OpenAI Chatbot**
2. Look for the blue "Debug Information" panel at the top of the page
3. **REPORT BACK WITH THESE VALUES:**

   - **API Key in database:** Present/Empty?
   - **Assistant ID in database:** Present/Empty?
   - **Form submission method:** GET/POST?

## STEP 3: Test Database Operations

1. In the admin sidebar, click the **"Test Database"** button
2. **REPORT BACK:** What message do you see?
   - ✅ "Database operations test successful!" 
   - ❌ "Database operations test failed!" (with details)

## STEP 4: Test API Settings Specifically

1. In the admin sidebar, click the **"Test API Save"** button
2. **REPORT BACK:** What message do you see?
   - ✅ "API settings test save successful!"
   - ❌ "API settings test save failed!" (with details)

## STEP 5: Test Main Form Submission

1. Enter your real API key and Assistant ID in the main form
2. Open browser developer tools (Press F12)
3. Go to the **Console** tab
4. Click **"Save Settings"**
5. **REPORT BACK:**
   - What success/error message appears on the page?
   - What debug messages appear in the browser console?
   - Does the Debug Information panel change after submission?

## STEP 6: Check Debug Log

1. Look for the file `/wp-content/debug.log` on your server
2. Search for entries containing "OpenAI Chatbot" (especially recent ones)
3. **REPORT BACK:** What log entries do you see? Look for patterns like:
   ```
   OpenAI Chatbot: Form submission detected
   OpenAI Chatbot: Processing form submission
   OpenAI Chatbot: Saving openai_chatbot_api_key = sk-***, result = success
   ```

## EXPECTED RESULTS vs ACTUAL RESULTS

### If Everything Works Correctly:

**Debug Information Panel (after form submission):**
- API Key in database: Present (sk-...)
- Assistant ID in database: Present (asst_...)
- Form submission method: POST
- POST submit button: Yes
- POST nonce present: Yes
- Nonce verification: PASSED
- POST API Key present: Yes
- POST Assistant ID present: Yes

**Test Results:**
- Database Test: ✅ "Database operations test successful!"
- API Settings Test: ✅ "API settings test save successful!"

**Browser Console:**
```
OpenAI Chatbot: Form submission detected
API Key present: yes
Assistant ID present: yes
OpenAI Chatbot: Form validation passed, submitting...
```

**Debug Log:**
```
OpenAI Chatbot: Form submission detected
OpenAI Chatbot: Nonce present: yes
OpenAI Chatbot: Nonce verification: passed
OpenAI Chatbot: Processing form submission
OpenAI Chatbot: API Key present in POST: yes
OpenAI Chatbot: Assistant ID present in POST: yes
OpenAI Chatbot: Saving openai_chatbot_api_key = sk-***, result = success, verified = yes
OpenAI Chatbot: Saving openai_chatbot_assistant_id = asst_xyz, result = success, verified = yes
OpenAI Chatbot: Settings save summary - X/X settings saved successfully
```

## COMMON FAILURE PATTERNS

### Pattern 1: Database Issues
**Symptoms:** Database Test fails
**Debug Panel Shows:** Normal values
**Log Shows:** Database write errors
**Solution:** Database permissions issue

### Pattern 2: Form Submission Issues
**Symptoms:** No POST data in Debug Panel
**Debug Panel Shows:** Form submission method: GET
**Log Shows:** No "Form submission detected"
**Solution:** Form HTML or JavaScript issue

### Pattern 3: Nonce Issues
**Symptoms:** Form detected but not processed
**Debug Panel Shows:** POST submit button: Yes, Nonce verification: FAILED
**Log Shows:** "Form submission detected" but no "Processing form submission"
**Solution:** Security token issue

### Pattern 4: Option Name Conflicts
**Symptoms:** Database Test passes, API Settings Test fails
**Debug Panel Shows:** Values don't persist after save
**Log Shows:** Save operations appear successful
**Solution:** Another plugin using same option names

## IMMEDIATE ACTIONS BASED ON RESULTS

### If Debug Information Panel Shows "Empty" for Both Settings:
1. Check if form submission method is POST
2. Check if nonce verification passes
3. Look for error messages in debug log

### If Database Test Fails:
1. Contact hosting provider about database permissions
2. Check WordPress database configuration
3. Try deactivating other plugins temporarily

### If API Settings Test Fails But Database Test Passes:
1. Check for plugin conflicts
2. Look for option name conflicts in debug log
3. Try with a different option name temporarily

### If Main Form Submission Shows No Console Messages:
1. Check for JavaScript errors in browser console
2. Verify form HTML structure
3. Test with default WordPress theme

## NEXT STEPS

**Please run through Steps 1-6 above and report back with:**

1. **Debug Information Panel values** (all of them)
2. **Database Test result** (exact message)
3. **API Settings Test result** (exact message)
4. **Browser Console output** (copy/paste any messages)
5. **Debug Log entries** (any lines containing "OpenAI Chatbot")
6. **Success/Error messages** that appear on the page

With this information, I can identify the exact point where the process is failing and provide a targeted fix.

## QUICK DIAGNOSTIC QUESTIONS

Before we dive deeper, please also answer:

1. **Are you using any caching plugins?** (WP Rocket, W3 Total Cache, etc.)
2. **Are you using any security plugins?** (Wordfence, Sucuri, etc.)
3. **What hosting provider are you using?**
4. **Are there any other OpenAI or chatbot plugins installed?**
5. **What WordPress version are you running?**

This information will help identify potential conflicts or hosting-specific issues.
