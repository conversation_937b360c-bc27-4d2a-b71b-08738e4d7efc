# OpenAI Chatbot Plugin - Admin Settings Guide

## Overview

The OpenAI Chatbot Plugin now includes a comprehensive admin settings interface that allows you to configure all aspects of your chatbot directly from the WordPress admin dashboard, without needing to edit wp-config.php files.

## Accessing the Settings

1. Log in to your WordPress admin dashboard
2. Navigate to **Settings > OpenAI Chatbot**
3. Configure your settings and click **Save Settings**

## Configuration Sections

### 1. API Configuration

This section allows you to configure the core OpenAI API settings:

#### OpenAI API Key
- **Field Type**: Password (with show/hide toggle)
- **Description**: Your OpenAI API key from https://platform.openai.com/api-keys
- **Priority**: Database setting overrides wp-config.php
- **Validation**: Must start with 'sk-'

#### Assistant ID
- **Field Type**: Text input
- **Description**: Your OpenAI Assistant ID from https://platform.openai.com/assistants
- **Priority**: Database setting overrides wp-config.php
- **Validation**: Must start with 'asst_'

#### Model Name
- **Field Type**: Dropdown selection
- **Available Models**:
  - GPT-4.1 (various versions)
  - GPT-4.5 Preview
  - GPT-4o (various versions including audio and realtime)
  - GPT-4o Mini (various versions)
- **Default**: gpt-4o

#### System Instructions
- **Field Type**: Large textarea
- **Description**: Define how your assistant should behave and respond
- **Default**: Helpful assistant for your website
- **Usage**: These instructions guide the AI's personality and response style

### 2. Tools Configuration

Configure which tools your assistant can use:

#### Available Tools
- **File Search**: Allows the assistant to search through uploaded files
- **Code Interpreter**: Enables code execution and analysis
- **Function Calling**: Allows custom function execution

#### Custom Functions
- **Field Type**: Code textarea (JSON format)
- **Description**: Define custom functions for advanced functionality
- **Validation**: JSON format validation with error highlighting
- **Documentation**: Links to OpenAI function calling documentation

### 3. General Settings

Standard chatbot configuration options:

- **Enable Chatbot**: Toggle chatbot on/off
- **Bot Name**: Display name in chat header
- **Welcome Message**: First message users see
- **Input Placeholder**: Placeholder text for message input
- **Powered By Text**: Footer text

### 4. Appearance

Visual customization options:

- **Position**: Bottom-right, bottom-left, top-right, top-left
- **Theme**: Default, dark, minimal
- **Show Timestamps**: Display message timestamps
- **Message Colors**: 
  - Bot message text color (with color picker)
  - User message text color (with color picker)
  - Live preview functionality
  - Accessibility contrast validation
  - Reset to defaults option
- **Custom CSS**: Advanced styling options

### 5. Behavior

Chatbot behavior settings:

- **Max Message Length**: Character limit per message (100-2000)
- **Max Messages in History**: Conversation history limit (10-200)
- **Disable On**: Select page types where chatbot should be hidden

### 6. Security & Rate Limiting

Protect your API usage:

- **Anonymous User Limit**: Messages per minute for non-logged users
- **Logged-in User Limit**: Messages per minute for registered users
- **Global Limit**: Total messages per minute across all users
- **Burst Limit**: Rapid-fire message protection

### 7. Advanced

Additional features:

- **Structured Data**: Add SEO-friendly JSON-LD markup

## Features

### Enhanced Security
- Password field with show/hide toggle for API key
- Input validation for API key and Assistant ID formats
- JSON validation for custom functions
- Sanitization of all user inputs

### User Experience
- Clean, professional WordPress admin interface
- Color picker with live preview
- Accessibility contrast checking
- Responsive design for mobile admin access
- Contextual help and documentation links

### Backward Compatibility
- Settings in database take priority over wp-config.php
- Existing wp-config.php settings still work as fallback
- Smooth migration from config-based to admin-based settings

## Migration from wp-config.php

If you currently have settings in wp-config.php:

1. The plugin will automatically use your existing wp-config.php settings
2. Enter your settings in the admin interface
3. The admin settings will take priority over wp-config.php
4. You can safely remove the wp-config.php definitions after configuring via admin

## API Connection Testing

Use the **Test API Connection** button in the sidebar to verify:
- API key validity
- Assistant ID accessibility
- Network connectivity to OpenAI

## Troubleshooting

### Common Issues

1. **API Key Invalid**: Ensure it starts with 'sk-' and is from OpenAI
2. **Assistant Not Found**: Verify the Assistant ID starts with 'asst_'
3. **JSON Errors**: Use the built-in validator for custom functions
4. **Rate Limits**: Adjust rate limiting settings if users are blocked

### Support Resources

- Plugin settings page includes links to OpenAI documentation
- Built-in usage statistics (if rate limiter is active)
- Error messages provide specific guidance

## Best Practices

1. **API Key Security**: Use the admin interface instead of wp-config.php for better security
2. **System Instructions**: Be specific about your assistant's role and limitations
3. **Rate Limiting**: Set appropriate limits based on your expected usage
4. **Tools**: Only enable tools your assistant actually needs
5. **Testing**: Use the API connection test before going live

## Technical Notes

- Settings are stored in WordPress options table
- Database settings override wp-config.php constants
- All inputs are properly sanitized and validated
- Color accessibility is automatically checked
- JSON validation prevents malformed custom functions
