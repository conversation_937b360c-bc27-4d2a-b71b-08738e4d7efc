/**
 * <PERSON><PERSON><PERSON> Chatbot Styles - Modern Light Design
 *
 * Comprehensive CSS for the chatbot interface with modern, clean design,
 * smooth animations, and excellent accessibility
 *
 * @package OpenAI_Chatbot
 */

/* Modern CSS Variables for light theme */
:root {
    --chatbot-primary-color: #e74266;
    --chatbot-primary-hover: #cf3c5c;
    --chatbot-primary-light: #f06292;
    --chatbot-accent-color: #1ca08a;
    --chatbot-secondary-color: #f8fafc;
    --chatbot-background: #ffffff;
    --chatbot-text-color: #1f2937;
    --chatbot-text-light: #6b7280;
    --chatbot-text-muted: #9ca3af;
    --chatbot-border-color: #e5e7eb;
    --chatbot-border-light: #f3f4f6;
    --chatbot-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    --chatbot-shadow-light: 0 4px 12px rgba(0, 0, 0, 0.05);
    --chatbot-shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
    --chatbot-border-radius: 20px;
    --chatbot-border-radius-small: 12px;
    --chatbot-border-radius-large: 24px;
    --chatbot-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --chatbot-transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --chatbot-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    --chatbot-z-index: 999999;

    /* Customizable message colors */
    --chatbot-bot-text-color: #1f2937;
    --chatbot-user-text-color: #ffffff;

    /* Gradient backgrounds with new color scheme */
    --chatbot-gradient-primary: linear-gradient(135deg, #e74266 0%, #cf3c5c 100%);
    --chatbot-gradient-secondary: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    --chatbot-gradient-message: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
    --chatbot-gradient-accent: linear-gradient(135deg, #1ca08a 0%, #16a085 100%);
}

/* Reset and base styles */
.openai-chatbot-widget,
.openai-chatbot-widget * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

.openai-chatbot-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: var(--chatbot-z-index);
    font-family: var(--chatbot-font-family);
    font-size: 14px;
    line-height: 1.4;
    color: var(--chatbot-text-color);
    direction: ltr;
    text-align: left;
}

/* Widget container responsive positioning */
@media (max-width: 768px) {
    .openai-chatbot-widget {
        bottom: 10px;
        right: 10px;
        left: 10px;
        width: auto;
    }
}

/* Modern toggle button */
.chatbot-toggle {
    position: relative;
    width: 64px;
    height: 64px;
    background: var(--chatbot-gradient-primary);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: var(--chatbot-shadow);
    transition: var(--chatbot-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    border: 3px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.chatbot-toggle:hover {
    background: var(--chatbot-gradient-primary);
    transform: scale(1.08) translateY(-2px);
    box-shadow: var(--chatbot-shadow-hover);
}

.chatbot-toggle:active {
    transform: scale(0.95) translateY(0);
}

.chatbot-toggle::before {
    content: '';
    position: absolute;
    inset: -2px;
    border-radius: 50%;
    background: var(--chatbot-gradient-primary);
    z-index: -1;
    opacity: 0;
    transition: var(--chatbot-transition);
}

.chatbot-toggle:hover::before {
    opacity: 0.3;
    transform: scale(1.2);
}

/* Toggle icons */
.toggle-icon {
    position: relative;
    width: 24px;
    height: 24px;
    color: white;
}

.toggle-icon svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transition: var(--chatbot-transition);
}

.openai-chatbot-widget[data-status="closed"] .toggle-icon .close-icon {
    opacity: 0;
    transform: rotate(90deg);
}

.openai-chatbot-widget[data-status="open"] .toggle-icon .chat-icon {
    opacity: 0;
    transform: rotate(-90deg);
}

/* Connection indicator */
.connection-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 12px;
    height: 12px;
}

.status-dot {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: #4caf50;
    transition: var(--chatbot-transition);
}

.status-dot.disconnected {
    background: #f44336;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Modern chat window */
.chatbot-window {
    position: absolute;
    bottom: 84px;
    right: 0;
    width: 400px;
    height: 600px;
    background: var(--chatbot-background);
    border-radius: var(--chatbot-border-radius-large);
    box-shadow: var(--chatbot-shadow);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    opacity: 0;
    transform: translateY(30px) scale(0.9);
    transition: var(--chatbot-transition);
    pointer-events: none;
    border: 1px solid var(--chatbot-border-light);
    backdrop-filter: blur(20px);
}

.openai-chatbot-widget[data-status="open"] .chatbot-window {
    opacity: 1;
    transform: translateY(0) scale(1);
    pointer-events: all;
}

.chatbot-window::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: var(--chatbot-border-radius-large);
    background: var(--chatbot-gradient-secondary);
    opacity: 0.5;
    z-index: -1;
}

/* Mobile responsive chat window */
@media (max-width: 768px) {
    .chatbot-window {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        top: 0;
        width: 100%;
        height: 100%;
        border-radius: 0;
        transform: translateY(100%);
    }
    
    .openai-chatbot-widget[data-status="open"] .chatbot-window {
        transform: translateY(0);
    }
}

/* Modern chat header */
.chatbot-header {
    background: var(--chatbot-gradient-primary);
    color: white;
    padding: 20px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
    border-radius: var(--chatbot-border-radius-large) var(--chatbot-border-radius-large) 0 0;
    position: relative;
}

.chatbot-header::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 16px;
}

.bot-avatar {
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    border: 2px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.bot-avatar svg {
    width: 24px;
    height: 24px;
}

.bot-info h3 {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 2px 0;
    letter-spacing: -0.01em;
}

.bot-info p {
    font-size: 13px;
    opacity: 0.85;
    margin: 0;
    font-weight: 400;
}

.minimize-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
    padding: 10px;
    border-radius: var(--chatbot-border-radius-small);
    transition: var(--chatbot-transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.minimize-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.minimize-btn svg {
    width: 20px;
    height: 20px;
}

/* Modern messages area */
.chatbot-messages {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: var(--chatbot-gradient-secondary);
    position: relative;
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 24px 20px;
    scroll-behavior: smooth;
    background: transparent;
}

/* Custom scrollbar */
.messages-container::-webkit-scrollbar {
    width: 6px;
}

.messages-container::-webkit-scrollbar-track {
    background: transparent;
}

.messages-container::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

/* Modern message styles */
.message {
    display: flex;
    margin-bottom: 20px;
    opacity: 0;
    transform: translateY(15px);
    transition: var(--chatbot-transition);
    align-items: flex-end;
    gap: 12px;
}

.message.message-visible {
    opacity: 1;
    transform: translateY(0);
}

.message-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    background: var(--chatbot-gradient-primary);
    color: white;
    box-shadow: var(--chatbot-shadow-light);
    border: 2px solid rgba(255, 255, 255, 0.8);
}

.message-avatar svg {
    width: 20px;
    height: 20px;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-avatar {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.message-bubble {
    max-width: 75%;
    position: relative;
}

.message-content {
    background: var(--chatbot-gradient-message);
    padding: 16px 20px;
    border-radius: var(--chatbot-border-radius);
    box-shadow: var(--chatbot-shadow-light);
    word-wrap: break-word;
    line-height: 1.6;
    font-size: 14px;
    border: 1px solid var(--chatbot-border-light);
    position: relative;
}

.user-message .message-content {
    background: var(--chatbot-gradient-primary);
    color: var(--chatbot-user-text-color);
    border: none;
    border-radius: var(--chatbot-border-radius) var(--chatbot-border-radius-small) var(--chatbot-border-radius-small) var(--chatbot-border-radius);
}

.bot-message .message-content {
    background: var(--chatbot-background);
    border: 1px solid var(--chatbot-border-light);
    color: var(--chatbot-bot-text-color);
    border-radius: var(--chatbot-border-radius-small) var(--chatbot-border-radius) var(--chatbot-border-radius) var(--chatbot-border-radius-small);
}

.system-message .message-content {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    font-style: italic;
    text-align: center;
}

.error-message .message-content {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.message-time {
    font-size: 11px;
    color: var(--chatbot-text-light);
    margin-top: 4px;
    text-align: right;
}

.user-message .message-time {
    text-align: left;
}

/* Message content formatting */
.message-content p {
    margin: 0 0 8px 0;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content a {
    color: var(--chatbot-primary-color);
    text-decoration: none;
}

.message-content a:hover {
    text-decoration: underline;
}

.user-message .message-content a {
    color: rgba(255, 255, 255, 0.9);
}

/* Modern typing indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 20px 24px;
    background: var(--chatbot-background);
    border-top: 1px solid var(--chatbot-border-light);
    backdrop-filter: blur(10px);
}

.typing-dots {
    display: flex;
    gap: 6px;
    align-items: center;
}

.typing-dots span {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--chatbot-primary-color);
    animation: typing 1.6s infinite ease-in-out;
    box-shadow: var(--chatbot-shadow-light);
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }
.typing-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.6);
        opacity: 0.4;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.typing-text {
    font-size: 13px;
    color: var(--chatbot-text-muted);
    font-style: italic;
    font-weight: 500;
}

/* Modern input area */
.chatbot-input {
    background: var(--chatbot-background);
    border-top: 1px solid var(--chatbot-border-light);
    flex-shrink: 0;
    border-radius: 0 0 var(--chatbot-border-radius-large) var(--chatbot-border-radius-large);
    position: relative;
}

.chatbot-input::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, var(--chatbot-border-light) 50%, transparent 100%);
}

.input-container {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    padding: 20px 24px;
}

.message-input {
    flex: 1;
    border: 2px solid var(--chatbot-border-light);
    border-radius: var(--chatbot-border-radius);
    padding: 16px 20px;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
    resize: none;
    outline: none;
    transition: var(--chatbot-transition-fast);
    min-height: 52px;
    max-height: 120px;
    background: var(--chatbot-background);
    color: var(--chatbot-text-color);
    box-shadow: var(--chatbot-shadow-light);
}

.message-input:focus {
    border-color: var(--chatbot-primary-color);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
    transform: translateY(-1px);
}

.message-input::placeholder {
    color: var(--chatbot-text-muted);
    font-weight: 400;
}

.send-button {
    width: 52px;
    height: 52px;
    background: var(--chatbot-gradient-primary);
    border: none;
    border-radius: var(--chatbot-border-radius);
    color: white;
    box-shadow: var(--chatbot-shadow-light);
    transition: var(--chatbot-transition-fast);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--chatbot-transition-fast);
    flex-shrink: 0;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.send-button:hover:not(:disabled) {
    background: var(--chatbot-gradient-primary);
    transform: scale(1.08) translateY(-2px);
    box-shadow: var(--chatbot-shadow);
}

.send-button:active:not(:disabled) {
    transform: scale(0.95) translateY(0);
}

.send-button:disabled {
    background: var(--chatbot-text-muted);
    cursor: not-allowed;
    transform: none;
    opacity: 0.5;
}

.send-button svg {
    width: 22px;
    height: 22px;
    transition: var(--chatbot-transition-fast);
}

.loading-icon {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Modern input footer */
.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 24px 16px;
    font-size: 12px;
    color: var(--chatbot-text-muted);
    background: var(--chatbot-background);
}

.character-count.warning {
    color: #ef4444;
    font-weight: 600;
}

.powered-by {
    opacity: 0.6;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Welcome message */
.welcome-message {
    margin-bottom: 20px;
}

.welcome-message .message {
    opacity: 1;
    transform: none;
}

/* Quick action buttons (like in the reference image) */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin: 16px 0;
    padding: 0 20px;
}

.quick-action-btn {
    background: var(--chatbot-gradient-primary);
    color: white;
    border: none;
    border-radius: var(--chatbot-border-radius);
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--chatbot-transition-fast);
    text-align: center;
    box-shadow: var(--chatbot-shadow-light);
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--chatbot-shadow);
    background: var(--chatbot-gradient-primary);
}

.quick-action-btn:active {
    transform: translateY(0);
}

/* Message timestamp styling */
.message-time {
    font-size: 11px;
    color: var(--chatbot-text-muted);
    margin-top: 6px;
    text-align: right;
    font-weight: 400;
    opacity: 0.8;
}

.user-message .message-time {
    text-align: left;
}

/* Accessibility improvements */
.chatbot-toggle:focus,
.minimize-btn:focus,
.send-button:focus,
.message-input:focus {
    outline: 2px solid var(--chatbot-primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --chatbot-border-color: #000;
        --chatbot-text-light: #000;
        --chatbot-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    }
    
    .message-content {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .messages-container {
        scroll-behavior: auto;
    }
}

/* Print styles */
@media print {
    .openai-chatbot-widget {
        display: none;
    }
}

/* RTL support */
[dir="rtl"] .openai-chatbot-widget {
    left: 20px;
    right: auto;
}

[dir="rtl"] .chatbot-window {
    left: 0;
    right: auto;
}

[dir="rtl"] .user-message {
    flex-direction: row;
}

[dir="rtl"] .message-time {
    text-align: left;
}

[dir="rtl"] .user-message .message-time {
    text-align: right;
}

/* Custom theme classes */
.openai-chatbot-widget.theme-dark {
    --chatbot-primary-color: #4f94cd;
    --chatbot-primary-hover: #6ba3d6;
    --chatbot-secondary-color: #2c2c2c;
    --chatbot-text-color: #ffffff;
    --chatbot-text-light: #b0b0b0;
    --chatbot-border-color: #444;
}

.openai-chatbot-widget.theme-minimal {
    --chatbot-border-radius: 4px;
    --chatbot-border-radius-small: 4px;
    --chatbot-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --chatbot-shadow-light: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* Quick Questions Styles */
.quick-questions {
    margin: 16px 0;
    padding: 0 20px;
}

.quick-questions-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.quick-question-btn {
    background: var(--chatbot-gradient-accent);
    color: white;
    border: none;
    border-radius: var(--chatbot-border-radius-small);
    padding: 14px 18px;
    font-size: 14px;
    font-family: var(--chatbot-font-family);
    font-weight: 500;
    cursor: pointer;
    transition: var(--chatbot-transition);
    text-align: left;
    line-height: 1.4;
    box-shadow: var(--chatbot-shadow-light);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.quick-question-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--chatbot-transition);
}

.quick-question-btn:hover {
    background: var(--chatbot-gradient-accent);
    transform: translateY(-2px);
    box-shadow: var(--chatbot-shadow);
    filter: brightness(1.1);
}

.quick-question-btn:hover::before {
    left: 100%;
}

.quick-question-btn:active {
    transform: translateY(0);
}

.quick-question-btn:focus {
    outline: 2px solid var(--chatbot-accent-color);
    outline-offset: 2px;
}

/* Welcome message enhancements */
.welcome-message {
    position: relative;
    margin-bottom: 24px;
}

.welcome-message::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: var(--chatbot-gradient-accent);
    border-radius: 1px;
    opacity: 0.6;
}

.welcome-message .message {
    margin-bottom: 0;
}

.welcome-message .message-content {
    background: var(--chatbot-gradient-message);
    border: 1px solid var(--chatbot-border-light);
    position: relative;
    overflow: hidden;
}

.welcome-message .message-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--chatbot-gradient-accent);
}

/* Mobile responsive quick questions */
@media (max-width: 768px) {
    .quick-questions {
        padding: 0 16px;
        margin: 12px 0;
    }

    .quick-question-btn {
        padding: 12px 16px;
        font-size: 13px;
    }

    .welcome-message {
        margin-bottom: 20px;
    }
}

/* Enhanced animations and micro-interactions */
.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.fade-out {
    animation: fadeOut 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

/* Smooth focus transitions */
.message-input:focus,
.quick-question-btn:focus {
    animation: focusGlow 0.3s ease-out;
}

@keyframes focusGlow {
    0% {
        box-shadow: 0 0 0 0 rgba(231, 66, 102, 0.4);
    }
    100% {
        box-shadow: 0 0 0 4px rgba(231, 66, 102, 0.1);
    }
}

/* Improved accessibility and visual feedback */
.chatbot-toggle:focus-visible,
.minimize-btn:focus-visible,
.send-button:focus-visible {
    outline: 2px solid var(--chatbot-primary-color);
    outline-offset: 2px;
}

/* Enhanced hover states for better UX */
.message:hover .message-content {
    transform: translateY(-1px);
    box-shadow: var(--chatbot-shadow);
    transition: var(--chatbot-transition);
}

.user-message:hover .message-content {
    filter: brightness(1.05);
}

/* Loading state improvements */
.send-button.loading {
    pointer-events: none;
}

.send-button.loading .send-icon {
    opacity: 0;
}

.send-button.loading .loading-icon {
    opacity: 1;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

.slide-down {
    animation: slideDown 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Utility classes */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.no-scroll {
    overflow: hidden;
}

/* Mobile-specific adjustments */
@media (max-width: 480px) {
    .chatbot-toggle {
        width: 60px;
        height: 60px;
    }

    .toggle-icon {
        width: 24px;
        height: 24px;
    }

    .message-bubble {
        max-width: 88%;
    }

    .input-container {
        padding: 16px 20px;
    }

    .message-input {
        font-size: 16px; /* Prevent zoom on iOS */
        padding: 14px 18px;
        min-height: 48px;
    }

    .send-button {
        width: 48px;
        height: 48px;
    }

    .messages-container {
        padding: 20px 16px;
    }

    .chatbot-header {
        padding: 16px 20px;
    }

    .quick-actions {
        padding: 0 16px;
    }
}

/* Tablet adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
    .chatbot-window {
        width: 380px;
        height: 560px;
    }

    .chatbot-toggle {
        width: 62px;
        height: 62px;
    }
}

/* Large screen adjustments */
@media (min-width: 1200px) {
    .chatbot-window {
        width: 420px;
        height: 640px;
    }

    .chatbot-toggle {
        width: 68px;
        height: 68px;
    }

    .toggle-icon {
        width: 26px;
        height: 26px;
    }

    .messages-container {
        padding: 28px 24px;
    }

    .input-container {
        padding: 24px 28px;
    }
}