# WordPress OpenAI Chatbot Plugin v1.7.1

A professional WordPress plugin that integrates OpenAI's GPT models to provide an intelligent, customizable chatbot for your website with advanced theming and rate limiting capabilities.

## 🚀 Key Features

### AI-Powered Intelligence
- **OpenAI GPT Integration**: Leverages latest GPT models for natural conversations
- **Intelligent Responses**: Context-aware AI responses with customizable parameters
- **Multiple Model Support**: Choose from various OpenAI models

### Advanced Customization
- **Enhanced CSS Variable System**: 60+ customizable variables for complete theming control
- **Preset Color Combinations**: 6 beautiful preset themes (Default Pink, Ocean Blue, Nature Green, Royal Purple, Sunset Orange, Dark Mode)
- **Automatic Color Harmony**: Generates hover states and color variants automatically
- **Dynamic Gradients**: Self-updating gradients that match your color scheme
- **Responsive Design**: Optimized for all devices and screen sizes

### Professional Features
- **Message Rate Limiting**: Configurable rate limiting to prevent abuse
- **Security Hardened**: Built-in security measures and API key protection
- **Performance Optimized**: Efficient CSS variables and minimal JavaScript overhead
- **Error Handling**: Graceful fallbacks and comprehensive error management

### Flexible Display Options
- **Floating Widget**: Elegant floating chatbot widget
- **Shortcode Support**: Embed anywhere with `[openai_chatbot]`
- **Multiple Positioning**: Choose optimal placement for your site

## 📦 Quick Installation

1. Upload the plugin folder to `/wp-content/plugins/`
2. Activate the plugin in WordPress admin
3. Go to **Settings > OpenAI Chatbot**
4. Add your OpenAI API key
5. Customize colors and settings
6. Your chatbot is ready!

## ⚙️ Configuration Options

### API Settings
- **OpenAI API Key**: Your OpenAI API key (required)
- **Model Selection**: Choose optimal GPT model
- **Response Parameters**: Control creativity and length

### Enhanced Theming
- **Custom Colors**: Primary, secondary, accent, and text colors
- **Preset Themes**: 6 professional color combinations
- **Automatic Variants**: System generates hover and focus states
- **Dynamic Effects**: Shadows and gradients that match your brand

### Rate Limiting & Security
- **Message Limits**: Prevent abuse with configurable limits
- **Time Windows**: Set rate limiting periods
- **Security Features**: Built-in protection measures

## 🎨 Available Themes

1. **Default Pink** - Professional pink and teal combination
2. **Ocean Blue** - Calming blue tones
3. **Nature Green** - Fresh green palette
4. **Royal Purple** - Elegant purple scheme
5. **Sunset Orange** - Warm orange gradients
6. **Dark Mode** - Sophisticated dark theme

## 📱 Usage

### Automatic Widget
The chatbot appears as a floating widget on your website automatically.

### Shortcode
Use `[openai_chatbot]` to embed the chatbot in any post, page, or widget area.

## 🔧 System Requirements

- WordPress 5.0+
- PHP 7.4+
- OpenAI API account
- Modern web browser

## 📚 Documentation Included

- **INSTALLATION.md** - Detailed setup guide
- **ADMIN_SETTINGS_GUIDE.md** - Complete admin documentation
- **CSS_VARIABLE_SYSTEM.md** - Advanced theming guide
- **TECHNICAL_ARCHITECTURE.md** - Developer documentation

## 🆕 What's New in v1.6.1

✨ **Enhanced CSS Variable System** - 60+ variables for complete control
🎨 **Preset Color Themes** - 6 professional color combinations
🔒 **Advanced Rate Limiting** - Configurable message limits
⚡ **Performance Improvements** - Faster loading and responses
🛡️ **Security Enhancements** - Better API key protection
🎯 **Automatic Color Harmony** - Smart color variant generation
📱 **Better Mobile Experience** - Enhanced responsive design

## 🏢 Professional Support

Developed and maintained by [Adnan Digital](https://adnandigital.com)

For support, custom development, or enterprise features, please contact us.

---

**Transform your website with AI-powered conversations today!**
