# OpenAI Chatbot Plugin - Settings Not Saving Troubleshooting Guide

## Issue Description
API Key and Assistant ID are not being saved through the admin interface despite form submission appearing to work.

## Debugging Tools Added

I've added several debugging tools to help identify the exact issue:

### 1. Debug Information Panel
- **Location**: Top of admin settings page (only visible when `WP_DEBUG` is enabled)
- **Shows**: Current database values, request method, POST data status

### 2. Enhanced Logging
- **Location**: WordPress debug log (`/wp-content/debug.log`)
- **Shows**: Form submission detection, nonce verification, setting save operations

### 3. Database Test Button
- **Location**: Admin sidebar
- **Purpose**: Tests basic database read/write/delete operations

### 4. API Settings Test Button
- **Location**: Admin sidebar  
- **Purpose**: Tests saving API key and Assistant ID specifically

### 5. Browser Console Logging
- **Location**: Browser developer tools console
- **Shows**: Form submission details and validation status

## Step-by-Step Troubleshooting

### Step 1: Enable Debug Mode
Add these lines to your `wp-config.php` file:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

### Step 2: Check Debug Information Panel
1. Go to **Settings > OpenAI Chatbot**
2. Look for the blue "Debug Information" panel at the top
3. Note the current values:
   - API Key in database: Present/Empty
   - Assistant ID in database: Present/Empty
   - Form submission method: GET/POST

### Step 3: Test Database Operations
1. In the admin sidebar, click **"Test Database"**
2. Expected result: "Database operations test successful!"
3. If it fails, there's a fundamental database issue

### Step 4: Test API Settings Specifically
1. In the admin sidebar, find **"API Settings Test"**
2. Click **"Test API Save"** (uses default test values)
3. Expected result: "API settings test save successful!"
4. If it fails, there's an issue with the specific option names

### Step 5: Test Main Form Submission
1. Enter your real API key and Assistant ID in the main form
2. Open browser developer tools (F12)
3. Go to the Console tab
4. Click **"Save Settings"**
5. Check console for debug messages
6. Check for success/error messages on the page

### Step 6: Check Debug Logs
1. Look for the file `/wp-content/debug.log`
2. Search for entries containing "OpenAI Chatbot"
3. Look for patterns like:
   ```
   OpenAI Chatbot: Form submission detected
   OpenAI Chatbot: Processing form submission
   OpenAI Chatbot: Saving openai_chatbot_api_key = sk-***, result = success
   ```

## Common Issues and Solutions

### Issue 1: Form Not Submitting
**Symptoms**: No debug messages in console or logs
**Causes**: JavaScript errors, form action issues
**Solution**: Check browser console for JavaScript errors

### Issue 2: Nonce Verification Failing
**Symptoms**: "Form submission detected" but no "Processing form submission"
**Causes**: Security token mismatch
**Solution**: Clear browser cache, check for plugin conflicts

### Issue 3: Database Write Permissions
**Symptoms**: Database test fails
**Causes**: Database permissions, hosting restrictions
**Solution**: Contact hosting provider, check database user permissions

### Issue 4: Option Name Conflicts
**Symptoms**: Settings appear to save but don't persist
**Causes**: Another plugin using same option names
**Solution**: Check for plugin conflicts, deactivate other plugins temporarily

### Issue 5: Caching Issues
**Symptoms**: Settings save but don't appear in form
**Causes**: Object caching, page caching
**Solution**: Clear all caches, disable caching plugins temporarily

## Diagnostic Commands

### Check Current Database Values
Add this to a temporary PHP file or use WordPress CLI:
```php
echo "API Key: " . (get_option('openai_chatbot_api_key') ? 'Present' : 'Empty') . "\n";
echo "Assistant ID: " . (get_option('openai_chatbot_assistant_id') ? 'Present' : 'Empty') . "\n";
```

### Manual Database Test
```php
// Test write
$result = update_option('test_option_123', 'test_value');
echo "Write result: " . ($result ? 'Success' : 'Failed') . "\n";

// Test read
$value = get_option('test_option_123');
echo "Read result: " . $value . "\n";

// Clean up
delete_option('test_option_123');
```

## Expected Debug Log Output

When everything works correctly, you should see:
```
OpenAI Chatbot: Form submission detected
OpenAI Chatbot: Nonce present: yes
OpenAI Chatbot: Nonce verification: passed
OpenAI Chatbot: Processing form submission
OpenAI Chatbot: API Key present in POST: yes
OpenAI Chatbot: Assistant ID present in POST: yes
OpenAI Chatbot: Saving openai_chatbot_api_key = sk-***, result = success, verified = yes
OpenAI Chatbot: Saving openai_chatbot_assistant_id = asst_xyz, result = success, verified = yes
OpenAI Chatbot: Settings save summary - 25/25 settings saved successfully
```

## Next Steps Based on Results

### If Database Test Fails
- Contact hosting provider about database permissions
- Check for database corruption
- Verify WordPress database configuration

### If API Settings Test Fails
- Check for option name conflicts with other plugins
- Verify WordPress options table integrity
- Test with different option names

### If Main Form Fails But Tests Pass
- Check for JavaScript conflicts
- Verify form HTML structure
- Test with default WordPress theme

### If Everything Tests Successfully But Issue Persists
- Clear all caches (object cache, page cache, browser cache)
- Check for theme conflicts
- Test in WordPress safe mode (all plugins except this one disabled)

## Contact Information

If you continue to experience issues after following this guide:

1. **Provide Debug Log Output**: Copy relevant entries from debug.log
2. **Provide Test Results**: Results from all test buttons
3. **Provide Browser Console Output**: Any JavaScript errors or debug messages
4. **Provide Environment Info**: WordPress version, PHP version, hosting provider

This information will help identify the specific cause of the issue and provide a targeted solution.
