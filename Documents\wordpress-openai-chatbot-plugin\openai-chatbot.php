<?php
/**
 * Plugin Name: CR OpenAI Chatbot
 * Plugin URI: https://adnandigital.com/openai-chatbot
 * Description: A production-ready WordPress plugin that integrates OpenAI's Assistants API to create an intelligent website chatbot with secure API handling, conversation management, and responsive design.
 * Version: 1.8.2
 * Author: <PERSON><PERSON>
 * Author URI: https://adnandigital.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: openai-chatbot
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 *
 * @package OpenAI_Chatbot
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}



// Define plugin constants
define('OPENAI_CHATBOT_VERSION', '1.8.2');
define('OPENAI_CHATBOT_PLUGIN_URL', plugin_dir_url(__FILE__));
define('OPENAI_CHATBOT_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('OPENAI_CHATBOT_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main OpenAI Chatbot Plugin Class
 * 
 * Handles plugin initialization, asset loading, and core functionality
 */
class OpenAI_Chatbot_Plugin {

    /**
     * Single instance of the plugin
     * 
     * @var OpenAI_Chatbot_Plugin
     */
    private static $instance = null;

    /**
     * OpenAI API handler instance
     * 
     * @var OpenAI_Chatbot_API
     */
    private $api_handler;

    /**
     * Get single instance of the plugin
     * 
     * @return OpenAI_Chatbot_Plugin
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor - Initialize the plugin
     */
    private function __construct() {
        $this->init_hooks();
        $this->load_dependencies();
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Plugin activation and deactivation
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));

        // Initialize plugin after WordPress loads
        add_action('init', array($this, 'init'));
        
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));

        // Add REST API endpoints
        add_action('rest_api_init', array($this, 'register_rest_routes'));

        // Add chat widget to footer
        add_action('wp_footer', array($this, 'render_chat_widget'));

        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));

        // Add shortcode support
        add_shortcode('openai_chatbot', array($this, 'chatbot_shortcode'));

        // AJAX handlers for logged-in and non-logged-in users
        add_action('wp_ajax_openai_chat', array($this, 'handle_chat_request'));
        add_action('wp_ajax_nopriv_openai_chat', array($this, 'handle_chat_request'));
    }

    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        require_once OPENAI_CHATBOT_PLUGIN_PATH . 'includes/class-openai-api.php';
        require_once OPENAI_CHATBOT_PLUGIN_PATH . 'includes/class-rate-limiter.php';
        require_once OPENAI_CHATBOT_PLUGIN_PATH . 'includes/class-security-handler.php';

        // Don't initialize API handler immediately - do it lazily when needed
        $this->api_handler = null;
    }

    /**
     * Initialize API handler with proper error handling
     * Only initialize when actually needed, not during plugin loading
     */
    private function init_api_handler() {
        // Don't initialize API handler during admin page loads or if already failed
        if (is_admin() && !$this->is_api_needed()) {
            return;
        }

        try {
            $this->api_handler = new OpenAI_Chatbot_API();
        } catch (Exception $e) {
            // Log the error but don't break the plugin
            $this->api_handler = null;

            // Add admin notice if in admin area and not on settings page
            if (is_admin() && !$this->is_settings_page()) {
                add_action('admin_notices', function() use ($e) {
                    echo '<div class="notice notice-error"><p>';
                    echo __('OpenAI Chatbot: ', 'openai-chatbot') . esc_html($e->getMessage());
                    echo ' <a href="' . admin_url('options-general.php?page=openai-chatbot') . '">';
                    echo __('Configure Settings', 'openai-chatbot');
                    echo '</a></p></div>';
                });
            }
        }
    }

    /**
     * Check if we're on the settings page
     */
    private function is_settings_page() {
        return isset($_GET['page']) && $_GET['page'] === 'openai-chatbot';
    }

    /**
     * Check if API handler is actually needed right now
     */
    private function is_api_needed() {
        // Only initialize API for actual chatbot functionality, not admin pages
        return !is_admin() || (defined('DOING_AJAX') && DOING_AJAX);
    }

    /**
     * Get API handler instance, initializing if needed
     */
    public function get_api_handler() {
        // Only initialize if we actually need the API and it's not already initialized
        if ($this->api_handler === null && $this->is_api_needed()) {
            $this->init_api_handler();
        }
        return $this->api_handler;
    }

    /**
     * Plugin activation hook
     */
    public function activate() {
        // Check for required PHP version
        if (version_compare(PHP_VERSION, '7.4', '<')) {
            deactivate_plugins(OPENAI_CHATBOT_PLUGIN_BASENAME);
            wp_die(__('OpenAI Chatbot requires PHP 7.4 or higher.', 'openai-chatbot'));
        }

        // Check for OpenAI API key (database or wp-config)
        $api_key = get_option('openai_chatbot_api_key', '');
        if (empty($api_key) && defined('OPENAI_API_KEY')) {
            $api_key = OPENAI_API_KEY;
        }

        $assistant_id = get_option('openai_chatbot_assistant_id', '');
        if (empty($assistant_id) && defined('OPENAI_ASSISTANT_ID')) {
            $assistant_id = OPENAI_ASSISTANT_ID;
        }

        if (empty($api_key) || empty($assistant_id)) {
            add_action('admin_notices', array($this, 'api_key_missing_notice'));
        }

        // Create database tables if needed
        $this->create_database_tables();

        // Set default options
        $this->set_default_options();

        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Plugin deactivation hook
     */
    public function deactivate() {
        // Clean up transients
        $this->cleanup_transients();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Initialize plugin
     */
    public function init() {
        // Load text domain for translations
        load_plugin_textdomain('openai-chatbot', false, dirname(OPENAI_CHATBOT_PLUGIN_BASENAME) . '/languages');
    }

    /**
     * Enqueue frontend assets (CSS and JavaScript)
     */
    public function enqueue_frontend_assets() {
        // Only load on pages where chatbot should appear
        if (!$this->should_load_chatbot()) {
            return;
        }

        // Enqueue CSS with cache busting
        wp_enqueue_style(
            'openai-chatbot-style',
            OPENAI_CHATBOT_PLUGIN_URL . 'assets/css/chatbot.css',
            array(),
            OPENAI_CHATBOT_VERSION . '.' . time()
        );

        // Add custom colors CSS
        $this->add_custom_colors_css();

        // Enqueue JavaScript
        wp_enqueue_script(
            'openai-chatbot-script',
            OPENAI_CHATBOT_PLUGIN_URL . 'assets/js/chatbot.js',
            array('jquery'),
            OPENAI_CHATBOT_VERSION,
            true
        );

        // Clear settings cache before localizing to ensure fresh data
        $this->clear_settings_cache();

        // Get fresh settings from database
        $bot_name = get_option('openai_chatbot_bot_name', __('AI Assistant', 'openai-chatbot'));
        $welcome_message = get_option('openai_chatbot_welcome_message', __('Hello! How can I help you today?', 'openai-chatbot'));
        $placeholder = get_option('openai_chatbot_placeholder', __('Type your message...', 'openai-chatbot'));
        $powered_by_text = get_option('openai_chatbot_powered_by_text', __('Powered by OpenAI', 'openai-chatbot'));
        $quick_questions = get_option('openai_chatbot_quick_questions', "What products do you offer?\nHow can I contact support?\nWhat are your business hours?");
        $max_message_length = (int) get_option('openai_chatbot_max_message_length', 1000);
        $show_timestamps = (bool) get_option('openai_chatbot_show_timestamps', false);

        // Get color settings
        $primary_color = get_option('openai_chatbot_primary_color', '#e74266');
        $secondary_color = get_option('openai_chatbot_secondary_color', '#cf3c5c');
        $accent_color = get_option('openai_chatbot_accent_color', '#1ca08a');
        $bot_text_color = get_option('openai_chatbot_bot_text_color', '#1e1e1e');
        $user_text_color = get_option('openai_chatbot_user_text_color', '#ffffff');

        // Get rate limiting settings
        $rate_limit_anonymous = (int) get_option('openai_chatbot_rate_limit_anonymous', 5);
        $rate_limit_logged_in = (int) get_option('openai_chatbot_rate_limit_logged_in', 10);
        $rate_limit_global = (int) get_option('openai_chatbot_rate_limit_global', 100);
        $rate_limit_burst = (int) get_option('openai_chatbot_rate_limit_burst', 3);

        // Parse quick questions into array
        $quick_questions_array = array();
        if (!empty($quick_questions)) {
            $quick_questions_array = array_filter(array_map('trim', explode("\n", $quick_questions)));
        }
        
        // Localize script with AJAX URL, nonce, and ALL settings
        wp_localize_script('openai-chatbot-script', 'openai_chatbot_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'rest_url' => rest_url('openai-chatbot/v1/'),
            'nonce' => wp_create_nonce('wp_rest'),
            'strings' => array(
                'placeholder' => $placeholder,
                'send' => __('Send', 'openai-chatbot'),
                'error' => __('Sorry, something went wrong. Please try again.', 'openai-chatbot'),
                'rate_limit' => __('Please wait before sending another message.', 'openai-chatbot'),
                'connecting' => __('Connecting...', 'openai-chatbot'),
                'thinking' => __('AI is thinking...', 'openai-chatbot'),
                'permission_denied' => __('Permission denied. Please refresh the page and try again.', 'openai-chatbot'),
                'bot_name' => $bot_name,
                'welcome' => $welcome_message,
                'powered_by' => $powered_by_text
            ),
            'settings' => array(
                'max_message_length' => $max_message_length,
                'typing_delay' => 50,
                'auto_scroll' => true,
                'show_timestamps' => $show_timestamps,
                'quick_questions' => $quick_questions_array,
                'bot_name' => $bot_name,
                'welcome_message' => $welcome_message,
                'powered_by_text' => $powered_by_text,
                'colors' => array(
                    'primary' => $primary_color,
                    'secondary' => $secondary_color,
                    'accent' => $accent_color,
                    'bot_text' => $bot_text_color,
                    'user_text' => $user_text_color
                ),
                'rate_limiting' => array(
                    'anonymous_limit' => $rate_limit_anonymous,
                    'logged_in_limit' => $rate_limit_logged_in,
                    'global_limit' => $rate_limit_global,
                    'burst_limit' => $rate_limit_burst
                )
            )
        ));
    }

    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        // Only load on plugin settings page
        if ('settings_page_openai-chatbot' !== $hook) {
            return;
        }

        // Enqueue admin CSS
        wp_enqueue_style(
            'openai-chatbot-admin-style',
            OPENAI_CHATBOT_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            OPENAI_CHATBOT_VERSION
        );

        // Enqueue WordPress color picker
        wp_enqueue_style('wp-color-picker');
        wp_enqueue_script('wp-color-picker');

        // Enqueue admin JavaScript
        wp_enqueue_script(
            'openai-chatbot-admin-script',
            OPENAI_CHATBOT_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery', 'wp-color-picker'),
            OPENAI_CHATBOT_VERSION,
            true
        );
    }

    /**
     * Register REST API routes
     */
    public function register_rest_routes() {
        register_rest_route('openai-chatbot/v1', '/chat', array(
            'methods' => 'POST',
            'callback' => array($this, 'handle_rest_chat_request'),
            'permission_callback' => array($this, 'check_chat_permissions'),
            'args' => array(
                'message' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_textarea_field',
                    'validate_callback' => array($this, 'validate_message')
                ),
                'conversation_id' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field'
                )
            )
        ));
    }

    /**
     * Add custom colors CSS to the page
     */
    private function add_custom_colors_css() {
        // Get all color settings
        $primary_color = get_option('openai_chatbot_primary_color', '#e74266');
        $secondary_color = get_option('openai_chatbot_secondary_color', '#cf3c5c');
        $accent_color = get_option('openai_chatbot_accent_color', '#1ca08a');
        $bot_text_color = get_option('openai_chatbot_bot_text_color', '#1e1e1e');
        $user_text_color = get_option('openai_chatbot_user_text_color', '#ffffff');

        // Validate colors (ensure they're valid hex colors)
        $colors = array(
            'primary' => $primary_color,
            'secondary' => $secondary_color,
            'accent' => $accent_color,
            'bot_text' => $bot_text_color,
            'user_text' => $user_text_color
        );

        foreach ($colors as $key => $color) {
            if (!preg_match('/^#[a-f0-9]{6}$/i', $color)) {
                switch ($key) {
                    case 'primary':
                        $colors[$key] = '#e74266';
                        break;
                    case 'secondary':
                        $colors[$key] = '#cf3c5c';
                        break;
                    case 'accent':
                        $colors[$key] = '#1ca08a';
                        break;
                    case 'bot_text':
                        $colors[$key] = '#1e1e1e';
                        break;
                    case 'user_text':
                        $colors[$key] = '#ffffff';
                        break;
                }
            }
        }

        // Generate color variants for enhanced theming
        $primary_hover = $this->adjust_color_brightness($colors['primary'], -10);
        $primary_dark = $this->adjust_color_brightness($colors['primary'], -20);
        $accent_hover = $this->adjust_color_brightness($colors['accent'], -10);

        // Convert primary color to RGB for shadow effects
        $primary_rgb = $this->hex_to_rgb($colors['primary']);
        $shadow_focus = $primary_rgb ?
            "0 0 0 3px rgba({$primary_rgb['r']}, {$primary_rgb['g']}, {$primary_rgb['b']}, 0.1)" :
            "0 0 0 3px rgba(231, 66, 102, 0.1)";

        $custom_css = "
        /* Enhanced CSS Variables Override */
        .openai-chatbot-widget {
            /* Core Brand Colors */
            --chatbot-primary-color: {$colors['primary']} !important;
            --chatbot-primary-hover: {$primary_hover} !important;
            --chatbot-primary-dark: {$primary_dark} !important;
            --chatbot-secondary-color: {$colors['secondary']} !important;
            --chatbot-accent-color: {$colors['accent']} !important;
            --chatbot-accent-hover: {$accent_hover} !important;

            /* Message Colors */
            --chatbot-bot-text-color: {$colors['bot_text']} !important;
            --chatbot-user-text-color: {$colors['user_text']} !important;
            --chatbot-user-bg-color: {$colors['primary']} !important;

            /* Interactive States */
            --chatbot-border-focus: {$colors['primary']} !important;
            --chatbot-shadow-focus: {$shadow_focus} !important;
            --chatbot-focus-outline: 2px solid {$colors['primary']} !important;

            /* Enhanced Gradients */
            --chatbot-gradient-primary: linear-gradient(135deg, {$colors['primary']} 0%, {$colors['secondary']} 100%) !important;
            --chatbot-gradient-primary-hover: linear-gradient(135deg, {$primary_hover} 0%, {$primary_dark} 100%) !important;
            --chatbot-gradient-accent: linear-gradient(135deg, {$colors['accent']} 0%, {$accent_hover} 100%) !important;
        }

        /* High specificity selectors to ensure colors are applied */
        .openai-chatbot-widget .bot-message .message-content,
        .openai-chatbot-widget .bot-message .message-content p,
        .openai-chatbot-widget .bot-message .message-content span,
        .openai-chatbot-widget .bot-message .message-content div {
            color: {$colors['bot_text']} !important;
        }

        .openai-chatbot-widget .user-message .message-content,
        .openai-chatbot-widget .user-message .message-content p,
        .openai-chatbot-widget .user-message .message-content span,
        .openai-chatbot-widget .user-message .message-content div {
            color: {$colors['user_text']} !important;
            background: var(--chatbot-gradient-primary) !important;
        }

        /* Enhanced button and interactive element colors */
        .openai-chatbot-widget .send-button,
        .openai-chatbot-widget .chatbot-toggle,
        .openai-chatbot-widget .quick-action-btn {
            background: var(--chatbot-gradient-primary) !important;
        }

        .openai-chatbot-widget .send-button:hover:not(:disabled),
        .openai-chatbot-widget .chatbot-toggle:hover,
        .openai-chatbot-widget .quick-action-btn:hover {
            background: var(--chatbot-gradient-primary-hover) !important;
        }

        .openai-chatbot-widget .quick-question-btn {
            background: var(--chatbot-gradient-accent) !important;
        }

        .openai-chatbot-widget .quick-question-btn:hover {
            background: var(--chatbot-gradient-accent) !important;
            filter: brightness(1.1) !important;
        }

        /* Header styling with enhanced gradient */
        .openai-chatbot-widget .chatbot-header {
            background: var(--chatbot-gradient-primary) !important;
        }

        /* Enhanced focus states */
        .openai-chatbot-widget .message-input:focus {
            border-color: var(--chatbot-border-focus) !important;
            box-shadow: var(--chatbot-shadow-focus) !important;
        }

        .openai-chatbot-widget .send-button:focus-visible,
        .openai-chatbot-widget .chatbot-toggle:focus-visible,
        .openai-chatbot-widget .quick-question-btn:focus {
            outline: var(--chatbot-focus-outline) !important;
            outline-offset: 2px !important;
        }

        /* Shortcode version with enhanced variables */
        .openai-chatbot-shortcode {
            --chatbot-primary-color: {$colors['primary']} !important;
            --chatbot-primary-hover: {$primary_hover} !important;
            --chatbot-secondary-color: {$colors['secondary']} !important;
            --chatbot-accent-color: {$colors['accent']} !important;
            --chatbot-bot-text-color: {$colors['bot_text']} !important;
            --chatbot-user-text-color: {$colors['user_text']} !important;
            --chatbot-gradient-primary: linear-gradient(135deg, {$colors['primary']} 0%, {$colors['secondary']} 100%) !important;
            --chatbot-gradient-accent: linear-gradient(135deg, {$colors['accent']} 0%, {$accent_hover} 100%) !important;
        }

        .openai-chatbot-shortcode .bot-message .message-content,
        .openai-chatbot-shortcode .bot-message .message-content p,
        .openai-chatbot-shortcode .bot-message .message-content span,
        .openai-chatbot-shortcode .bot-message .message-content div {
            color: {$colors['bot_text']} !important;
        }

        .openai-chatbot-shortcode .user-message .message-content,
        .openai-chatbot-shortcode .user-message .message-content p,
        .openai-chatbot-shortcode .user-message .message-content span,
        .openai-chatbot-shortcode .user-message .message-content div {
            color: {$colors['user_text']} !important;
        }
        ";

        wp_add_inline_style('openai-chatbot-style', $custom_css);
    }

    /**
     * Utility function to adjust color brightness
     */
    private function adjust_color_brightness($hex, $percent) {
        // Remove # if present
        $hex = ltrim($hex, '#');

        // Convert to RGB
        $rgb = array(
            'r' => hexdec(substr($hex, 0, 2)),
            'g' => hexdec(substr($hex, 2, 2)),
            'b' => hexdec(substr($hex, 4, 2))
        );

        // Adjust brightness
        foreach ($rgb as $key => $value) {
            $rgb[$key] = max(0, min(255, round($value * (100 + $percent) / 100)));
        }

        // Convert back to hex
        return sprintf('#%02x%02x%02x', $rgb['r'], $rgb['g'], $rgb['b']);
    }

    /**
     * Utility function to convert hex to RGB
     */
    private function hex_to_rgb($hex) {
        $hex = ltrim($hex, '#');

        if (strlen($hex) !== 6) {
            return false;
        }

        return array(
            'r' => hexdec(substr($hex, 0, 2)),
            'g' => hexdec(substr($hex, 2, 2)),
            'b' => hexdec(substr($hex, 4, 2))
        );
    }

    /**
     * Handle REST API chat request
     */
    public function handle_rest_chat_request($request) {
        // Verify nonce for security
        $nonce = $request->get_header('X-WP-Nonce');

        if (!wp_verify_nonce($nonce, 'wp_rest')) {
            return new WP_Error('invalid_nonce', __('Invalid security token.', 'openai-chatbot'), array('status' => 403));
        }

        // Rate limiting check
        $rate_limiter = new OpenAI_Chatbot_Rate_Limiter();
        if (!$rate_limiter->check_rate_limit()) {
            return new WP_Error('rate_limit_exceeded', __('Rate limit exceeded. Please wait before sending another message.', 'openai-chatbot'), array('status' => 429));
        }

        // Get and validate parameters
        $message = $request->get_param('message');
        $conversation_id = $request->get_param('conversation_id');

        try {
            // Get API handler and ensure it's properly initialized
            $api_handler = $this->get_api_handler();
            if ($api_handler === null) {
                throw new Exception(__('API handler not properly configured.', 'openai-chatbot'));
            }

            // Send message to OpenAI API
            $response = $api_handler->send_message($message, $conversation_id);

            // Validate response structure
            if (!is_array($response) || !isset($response['message'])) {
                return new WP_Error('invalid_response', __('Invalid response from AI service.', 'openai-chatbot'), array('status' => 500));
            }

            return rest_ensure_response(array(
                'success' => true,
                'message' => $response['message'],
                'conversation_id' => $response['conversation_id'] ?? null,
                'timestamp' => current_time('timestamp')
            ));

        } catch (Exception $e) {
            return new WP_Error('api_error', __('Unable to process your request. Please try again later.', 'openai-chatbot'), array('status' => 500));
        }
    }

    /**
     * Check permissions for chat requests
     */
    public function check_chat_permissions($request) {
        // Allow all users to use chat (can be customized based on requirements)
        return true;
    }

    /**
     * Validate chat message
     */
    public function validate_message($value, $request, $param) {
        if (empty(trim($value))) {
            return new WP_Error('empty_message', __('Message cannot be empty.', 'openai-chatbot'));
        }

        if (strlen($value) > 1000) {
            return new WP_Error('message_too_long', __('Message is too long. Maximum 1000 characters allowed.', 'openai-chatbot'));
        }

        return true;
    }

    /**
     * Legacy AJAX handler for backward compatibility
     */
    public function handle_chat_request() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'openai_chatbot_nonce')) {
            wp_die(__('Security check failed.', 'openai-chatbot'));
        }

        // Create WP_REST_Request object and delegate to REST handler
        $request = new WP_REST_Request('POST', '/openai-chatbot/v1/chat');
        $request->set_param('message', sanitize_textarea_field($_POST['message']));
        $request->set_param('conversation_id', sanitize_text_field($_POST['conversation_id'] ?? ''));

        $response = $this->handle_rest_chat_request($request);
        
        if (is_wp_error($response)) {
            wp_send_json_error($response->get_error_message());
        } else {
            wp_send_json_success($response->data);
        }
    }

    /**
     * Clear chatbot settings cache
     */
    public function clear_settings_cache() {
        $settings_keys = [
            'openai_chatbot_enabled',
            'openai_chatbot_bot_name',
            'openai_chatbot_position',
            'openai_chatbot_theme',
            'openai_chatbot_welcome_message',
            'openai_chatbot_placeholder',
            'openai_chatbot_powered_by_text',
            'openai_chatbot_quick_questions',
            'openai_chatbot_show_timestamps',
            'openai_chatbot_max_message_length',
            'openai_chatbot_primary_color',
            'openai_chatbot_secondary_color',
            'openai_chatbot_accent_color',
            'openai_chatbot_bot_text_color',
            'openai_chatbot_user_text_color'
        ];

        foreach ($settings_keys as $key) {
            wp_cache_delete($key, 'options');
        }

        // Also clear any object cache
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
        
        // Update cache buster to force fresh data loading
        update_option('openai_chatbot_cache_buster', time());
        
        // Clear any transients that might cache settings
        delete_transient('openai_chatbot_settings_cache');
    }

    /**
     * Render chat widget HTML
     */
    public function render_chat_widget() {
        if (!$this->should_load_chatbot()) {
            return;
        }

        // Clear cache before rendering to ensure fresh data
        $this->clear_settings_cache();

        // Force clear all WordPress object cache
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }

        // Clear any persistent object cache
        if (function_exists('wp_cache_flush_group')) {
            wp_cache_flush_group('options');
        }
        
        // Add cache-busting parameter to force fresh data
        $cache_buster = get_option('openai_chatbot_cache_buster', time());
        if (!$cache_buster || (time() - $cache_buster) > 300) { // Update every 5 minutes max
            update_option('openai_chatbot_cache_buster', time());
        }

        include OPENAI_CHATBOT_PLUGIN_PATH . 'templates/chat-widget.php';
    }

    /**
     * Chatbot shortcode handler
     */
    public function chatbot_shortcode($atts) {
        $atts = shortcode_atts(array(
            'position' => 'inline',
            'height' => '400px',
            'width' => '100%'
        ), $atts, 'openai_chatbot');

        ob_start();
        include OPENAI_CHATBOT_PLUGIN_PATH . 'templates/chat-shortcode.php';
        return ob_get_clean();
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_options_page(
            __('OpenAI Chatbot Settings', 'openai-chatbot'),
            __('OpenAI Chatbot', 'openai-chatbot'),
            'manage_options',
            'openai-chatbot',
            array($this, 'admin_page')
        );
    }

    /**
     * Admin page callback
     */
    public function admin_page() {
        include OPENAI_CHATBOT_PLUGIN_PATH . 'admin/settings-page.php';
    }

    /**
     * Check if chatbot should load on current page
     */
    private function should_load_chatbot() {
        // Don't load in admin area
        if (is_admin()) {
            return false;
        }

        // Check if disabled on current page type
        $disabled_on = get_option('openai_chatbot_disabled_on', array());
        
        if (is_home() && in_array('home', $disabled_on)) {
            return false;
        }
        
        if (is_single() && in_array('posts', $disabled_on)) {
            return false;
        }
        
        if (is_page() && in_array('pages', $disabled_on)) {
            return false;
        }

        return true;
    }

    /**
     * Create database tables
     */
    private function create_database_tables() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'openai_chatbot_conversations';

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            conversation_id varchar(255) NOT NULL,
            user_id bigint(20) DEFAULT NULL,
            user_ip varchar(45) NOT NULL,
            message_count int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY conversation_id (conversation_id),
            KEY user_id (user_id),
            KEY created_at (created_at)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Set default plugin options
     */
    private function set_default_options() {
        $defaults = array(
            // API Configuration
            'openai_chatbot_api_key' => '',
            'openai_chatbot_assistant_id' => '',
            'openai_chatbot_system_instructions' => sprintf(
                __('You are a helpful assistant for %s. Be concise, friendly, and helpful. If you don\'t know something, say so honestly.', 'openai-chatbot'),
                get_bloginfo('name')
            ),
            'openai_chatbot_model_name' => 'gpt-4o',
            'openai_chatbot_tools_enabled' => array('file_search', 'code_interpreter'),
            'openai_chatbot_custom_tools' => '',

            // General Settings
            'openai_chatbot_enabled' => true,
            'openai_chatbot_bot_name' => __('AI Assistant', 'openai-chatbot'),
            'openai_chatbot_position' => 'bottom-right',
            'openai_chatbot_theme' => 'default',
            'openai_chatbot_welcome_message' => __('Hello! How can I help you today?', 'openai-chatbot'),
            'openai_chatbot_placeholder' => __('Type your message...', 'openai-chatbot'),
            'openai_chatbot_powered_by_text' => __('Powered by OpenAI', 'openai-chatbot'),
            'openai_chatbot_show_timestamps' => false,
            'openai_chatbot_max_message_length' => 1000,
            'openai_chatbot_max_messages' => 50,
            'openai_chatbot_rate_limit' => 10,
            'openai_chatbot_disabled_on' => array(),
            'openai_chatbot_primary_color' => '#e74266',
            'openai_chatbot_secondary_color' => '#cf3c5c',
            'openai_chatbot_accent_color' => '#1ca08a',
            'openai_chatbot_bot_text_color' => '#1f2937',
            'openai_chatbot_user_text_color' => '#ffffff',
            'openai_chatbot_quick_questions' => "What products do you offer?\nHow can I contact support?\nWhat are your business hours?"
        );

        foreach ($defaults as $option => $value) {
            if (get_option($option) === false) {
                add_option($option, $value);
            }
        }
    }

    /**
     * Clean up transients on deactivation
     */
    private function cleanup_transients() {
        global $wpdb;
        
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_openai_chatbot_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_openai_chatbot_%'");
    }

    /**
     * Display API key missing notice
     */
    public function api_key_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p>
                <?php _e('OpenAI Chatbot: Please configure your OpenAI API key and Assistant ID.', 'openai-chatbot'); ?>
                <a href="<?php echo admin_url('options-general.php?page=openai-chatbot'); ?>" class="button button-primary" style="margin-left: 10px;">
                    <?php _e('Configure Settings', 'openai-chatbot'); ?>
                </a>
            </p>
            <p>
                <?php _e('Alternatively, you can add them to wp-config.php:', 'openai-chatbot'); ?>
                <br><code>define('OPENAI_API_KEY', 'your-api-key-here');</code>
                <br><code>define('OPENAI_ASSISTANT_ID', 'your-assistant-id-here');</code>
            </p>
        </div>
        <?php
    }
}

// Initialize the plugin
OpenAI_Chatbot_Plugin::get_instance();