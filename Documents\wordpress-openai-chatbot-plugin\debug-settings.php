<?php
/**
 * Debug script to check current database values
 * 
 * This temporary script helps debug the settings synchronization issue
 * by showing what values are actually stored in the database
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // If not in WordPress context, try to load WordPress
    $wp_load_paths = [
        '../../../wp-load.php',
        '../../../../wp-load.php',
        '../../../../../wp-load.php'
    ];
    
    $wp_loaded = false;
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            $wp_loaded = true;
            break;
        }
    }
    
    if (!$wp_loaded) {
        die('WordPress not found. Please run this script from WordPress admin or place it in the correct directory.');
    }
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>OpenAI Chatbot Settings Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .setting { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        .setting-name { font-weight: bold; color: #0073aa; }
        .setting-value { margin-top: 5px; padding: 5px; background: #f9f9f9; border-radius: 3px; }
        .empty { color: #999; font-style: italic; }
        .different { background: #fff3cd; border-color: #ffeaa7; }
        .header { background: #0073aa; color: white; padding: 15px; margin: -20px -20px 20px -20px; }
        .refresh-btn { background: #0073aa; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin-bottom: 20px; }
        .refresh-btn:hover { background: #005a87; }
    </style>
</head>
<body>
    <div class="header">
        <h1>OpenAI Chatbot Settings Debug</h1>
        <p>Current database values vs expected defaults</p>
    </div>
    
    <button class="refresh-btn" onclick="location.reload()">Refresh Values</button>
    
    <?php
    // Define all settings to check
    $settings_to_check = [
        'openai_chatbot_enabled' => ['default' => true, 'type' => 'boolean'],
        'openai_chatbot_bot_name' => ['default' => 'AI Assistant', 'type' => 'string'],
        'openai_chatbot_welcome_message' => ['default' => 'Hello! How can I help you today?', 'type' => 'string'],
        'openai_chatbot_placeholder' => ['default' => 'Type your message...', 'type' => 'string'],
        'openai_chatbot_powered_by_text' => ['default' => 'Powered by OpenAI', 'type' => 'string'],
        'openai_chatbot_quick_questions' => ['default' => "What products do you offer?\nHow can I contact support?\nWhat are your business hours?", 'type' => 'textarea'],
        'openai_chatbot_position' => ['default' => 'bottom-right', 'type' => 'string'],
        'openai_chatbot_theme' => ['default' => 'default', 'type' => 'string'],
        'openai_chatbot_show_timestamps' => ['default' => false, 'type' => 'boolean'],
        'openai_chatbot_max_message_length' => ['default' => 1000, 'type' => 'integer'],
        'openai_chatbot_primary_color' => ['default' => '#e74266', 'type' => 'string'],
        'openai_chatbot_secondary_color' => ['default' => '#cf3c5c', 'type' => 'string'],
        'openai_chatbot_accent_color' => ['default' => '#1ca08a', 'type' => 'string'],
    ];
    
    echo '<h2>Settings Analysis</h2>';
    
    foreach ($settings_to_check as $setting_name => $config) {
        $current_value = get_option($setting_name);
        $default_value = $config['default'];
        $is_different = ($current_value !== $default_value);
        
        echo '<div class="setting' . ($is_different ? ' different' : '') . '">';
        echo '<div class="setting-name">' . esc_html($setting_name) . '</div>';
        
        echo '<div class="setting-value">';
        echo '<strong>Current Value:</strong> ';
        if ($current_value === false) {
            echo '<span class="empty">(not set in database)</span>';
        } elseif (empty($current_value) && $current_value !== 0 && $current_value !== false) {
            echo '<span class="empty">(empty)</span>';
        } else {
            if ($config['type'] === 'textarea') {
                echo '<pre>' . esc_html($current_value) . '</pre>';
            } elseif ($config['type'] === 'boolean') {
                echo $current_value ? 'true' : 'false';
            } else {
                echo esc_html($current_value);
            }
        }
        echo '</div>';
        
        echo '<div class="setting-value">';
        echo '<strong>Expected Default:</strong> ';
        if ($config['type'] === 'textarea') {
            echo '<pre>' . esc_html($default_value) . '</pre>';
        } elseif ($config['type'] === 'boolean') {
            echo $default_value ? 'true' : 'false';
        } else {
            echo esc_html($default_value);
        }
        echo '</div>';
        
        if ($is_different) {
            echo '<div style="color: #d63384; font-weight: bold;">⚠ Value differs from default</div>';
        }
        
        echo '</div>';
    }
    
    // Check if there are any other chatbot-related options
    global $wpdb;
    $all_chatbot_options = $wpdb->get_results(
        "SELECT option_name, option_value FROM {$wpdb->options} WHERE option_name LIKE 'openai_chatbot_%' ORDER BY option_name"
    );
    
    echo '<h2>All OpenAI Chatbot Options in Database</h2>';
    echo '<div style="background: #f0f0f0; padding: 15px; border-radius: 4px;">';
    
    if (empty($all_chatbot_options)) {
        echo '<p class="empty">No OpenAI Chatbot options found in database!</p>';
    } else {
        echo '<table style="width: 100%; border-collapse: collapse;">';
        echo '<tr style="background: #0073aa; color: white;"><th style="padding: 8px; text-align: left;">Option Name</th><th style="padding: 8px; text-align: left;">Value</th></tr>';
        
        foreach ($all_chatbot_options as $option) {
            echo '<tr style="border-bottom: 1px solid #ddd;">';
            echo '<td style="padding: 8px; font-family: monospace;">' . esc_html($option->option_name) . '</td>';
            echo '<td style="padding: 8px;">';
            
            if (strlen($option->option_value) > 100) {
                echo '<pre style="max-height: 100px; overflow-y: auto; background: white; padding: 5px; border: 1px solid #ddd;">' . esc_html($option->option_value) . '</pre>';
            } else {
                echo esc_html($option->option_value);
            }
            
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</table>';
    }
    
    echo '</div>';

    // Test form submission detection
    echo '<h2>Form Submission Test</h2>';
    echo '<div style="background: #e8f4f8; padding: 15px; border-radius: 4px;">';
    echo '<p>To test if form submission is working, try saving settings in the admin panel, then refresh this page.</p>';
    echo '<p><strong>Last modified times:</strong></p>';

    foreach (['openai_chatbot_bot_name', 'openai_chatbot_welcome_message', 'openai_chatbot_quick_questions'] as $key_setting) {
        $option_id = $wpdb->get_var($wpdb->prepare(
            "SELECT option_id FROM {$wpdb->options} WHERE option_name = %s",
            $key_setting
        ));

        if ($option_id) {
            echo '<p>' . esc_html($key_setting) . ': Option ID ' . $option_id . '</p>';
        } else {
            echo '<p>' . esc_html($key_setting) . ': <span class="empty">Not found in database</span></p>';
        }
    }

    echo '</div>';
    ?>

    <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 4px;">
        <h3>Instructions:</h3>
        <ol>
            <li>Note the current values above</li>
            <li>Go to the admin settings page and change some values</li>
            <li>Save the settings</li>
            <li>Come back here and refresh to see if values changed</li>
            <li>Check the frontend chatbot to see if it reflects the changes</li>
        </ol>
    </div>
    
    <p style="margin-top: 30px; color: #666; font-size: 12px;">
        Debug script created at: <?php echo date('Y-m-d H:i:s'); ?><br>
        WordPress version: <?php echo get_bloginfo('version'); ?><br>
        Plugin path: <?php echo OPENAI_CHATBOT_PLUGIN_PATH ?? 'Not defined'; ?>
    </p>
</body>
</html>
