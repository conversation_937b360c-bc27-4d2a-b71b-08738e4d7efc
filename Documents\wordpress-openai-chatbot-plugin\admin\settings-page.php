<?php
/**
 * Admin Settings Page
 *
 * Provides the WordPress admin interface for configuring the OpenAI Chatbot plugin
 * Includes settings for API configuration, appearance, behavior, and security
 *
 * @package OpenAI_Chatbot
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Debug: Log form submission attempt
if (defined('WP_DEBUG') && WP_DEBUG) {
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['openai_chatbot_settings_nonce'])) {
        error_log('OpenAI Chatbot: Form submission detected');
        error_log('OpenAI Chatbot: POST keys: ' . implode(', ', array_keys($_POST)));
        error_log('OpenAI Chatbot: Nonce present: ' . (isset($_POST['openai_chatbot_settings_nonce']) ? 'yes' : 'no'));
        if (isset($_POST['openai_chatbot_settings_nonce'])) {
            error_log('OpenAI Chatbot: Nonce verification: ' . (wp_verify_nonce($_POST['openai_chatbot_settings_nonce'], 'openai_chatbot_settings') ? 'passed' : 'failed'));
        }
    }
}

// Handle form submission - check for multiple possible submit button names
$form_submitted = (isset($_POST['submit']) || isset($_POST['submit_button']) || (isset($_POST['openai_chatbot_settings_nonce']) && $_SERVER['REQUEST_METHOD'] === 'POST'));

if ($form_submitted && wp_verify_nonce($_POST['openai_chatbot_settings_nonce'], 'openai_chatbot_settings')) {
    $settings_saved = false;
    $error_message = '';

    // Debug: Log that we're processing the form
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('OpenAI Chatbot: Processing form submission');
        error_log('OpenAI Chatbot: API Key present in POST: ' . (isset($_POST['openai_chatbot_api_key']) ? 'yes' : 'no'));
        error_log('OpenAI Chatbot: Assistant ID present in POST: ' . (isset($_POST['openai_chatbot_assistant_id']) ? 'yes' : 'no'));
    }

    try {
        // Save API configuration settings with proper validation
        $api_settings = array(
            'openai_chatbot_api_key' => isset($_POST['openai_chatbot_api_key']) ? sanitize_text_field($_POST['openai_chatbot_api_key']) : '',
            'openai_chatbot_assistant_id' => isset($_POST['openai_chatbot_assistant_id']) ? sanitize_text_field($_POST['openai_chatbot_assistant_id']) : '',
            'openai_chatbot_system_instructions' => isset($_POST['openai_chatbot_system_instructions']) ? wp_kses_post($_POST['openai_chatbot_system_instructions']) : '',
            'openai_chatbot_model_name' => isset($_POST['openai_chatbot_model_name']) ? sanitize_text_field($_POST['openai_chatbot_model_name']) : 'gpt-4o',
            'openai_chatbot_tools_enabled' => isset($_POST['openai_chatbot_tools_enabled']) ? array_map('sanitize_text_field', $_POST['openai_chatbot_tools_enabled']) : array(),
            'openai_chatbot_custom_tools' => isset($_POST['openai_chatbot_custom_tools']) ? sanitize_textarea_field($_POST['openai_chatbot_custom_tools']) : ''
        );

        // Save general settings with proper validation
        $settings = array(
            'openai_chatbot_enabled' => isset($_POST['openai_chatbot_enabled']) ? 1 : 0,
            'openai_chatbot_position' => isset($_POST['openai_chatbot_position']) ? sanitize_text_field($_POST['openai_chatbot_position']) : 'bottom-right',
            'openai_chatbot_theme' => isset($_POST['openai_chatbot_theme']) ? sanitize_text_field($_POST['openai_chatbot_theme']) : 'default',
            'openai_chatbot_bot_name' => isset($_POST['openai_chatbot_bot_name']) ? sanitize_text_field($_POST['openai_chatbot_bot_name']) : __('AI Assistant', 'openai-chatbot'),
            'openai_chatbot_welcome_message' => isset($_POST['openai_chatbot_welcome_message']) ? wp_kses_post($_POST['openai_chatbot_welcome_message']) : __('Hello! How can I help you today?', 'openai-chatbot'),
            'openai_chatbot_placeholder' => isset($_POST['openai_chatbot_placeholder']) ? sanitize_text_field($_POST['openai_chatbot_placeholder']) : __('Type your message...', 'openai-chatbot'),
            'openai_chatbot_powered_by_text' => isset($_POST['openai_chatbot_powered_by_text']) ? sanitize_text_field($_POST['openai_chatbot_powered_by_text']) : __('Powered by OpenAI', 'openai-chatbot'),
            'openai_chatbot_show_timestamps' => isset($_POST['openai_chatbot_show_timestamps']) ? 1 : 0,
            'openai_chatbot_max_message_length' => isset($_POST['openai_chatbot_max_message_length']) ? absint($_POST['openai_chatbot_max_message_length']) : 1000,
            'openai_chatbot_max_messages' => isset($_POST['openai_chatbot_max_messages']) ? absint($_POST['openai_chatbot_max_messages']) : 50,
            'openai_chatbot_disabled_on' => isset($_POST['openai_chatbot_disabled_on']) ? array_map('sanitize_text_field', $_POST['openai_chatbot_disabled_on']) : array(),
            'openai_chatbot_custom_css' => isset($_POST['openai_chatbot_custom_css']) ? sanitize_textarea_field($_POST['openai_chatbot_custom_css']) : '',
            'openai_chatbot_add_structured_data' => isset($_POST['openai_chatbot_add_structured_data']) ? 1 : 0,
            'openai_chatbot_primary_color' => isset($_POST['openai_chatbot_primary_color']) ? sanitize_hex_color($_POST['openai_chatbot_primary_color']) : '#e74266',
            'openai_chatbot_secondary_color' => isset($_POST['openai_chatbot_secondary_color']) ? sanitize_hex_color($_POST['openai_chatbot_secondary_color']) : '#cf3c5c',
            'openai_chatbot_accent_color' => isset($_POST['openai_chatbot_accent_color']) ? sanitize_hex_color($_POST['openai_chatbot_accent_color']) : '#1ca08a',
            'openai_chatbot_bot_text_color' => isset($_POST['openai_chatbot_bot_text_color']) ? sanitize_hex_color($_POST['openai_chatbot_bot_text_color']) : '#1e1e1e',
            'openai_chatbot_user_text_color' => isset($_POST['openai_chatbot_user_text_color']) ? sanitize_hex_color($_POST['openai_chatbot_user_text_color']) : '#ffffff',
            'openai_chatbot_quick_questions' => isset($_POST['openai_chatbot_quick_questions']) ? sanitize_textarea_field($_POST['openai_chatbot_quick_questions']) : '',
            'openai_chatbot_rate_limit_anonymous' => isset($_POST['openai_chatbot_rate_limit_anonymous']) ? absint($_POST['openai_chatbot_rate_limit_anonymous']) : 5,
            'openai_chatbot_rate_limit_logged_in' => isset($_POST['openai_chatbot_rate_limit_logged_in']) ? absint($_POST['openai_chatbot_rate_limit_logged_in']) : 10,
            'openai_chatbot_rate_limit_global' => isset($_POST['openai_chatbot_rate_limit_global']) ? absint($_POST['openai_chatbot_rate_limit_global']) : 100,
            'openai_chatbot_rate_limit_burst' => isset($_POST['openai_chatbot_rate_limit_burst']) ? absint($_POST['openai_chatbot_rate_limit_burst']) : 3
        );

        // Merge API settings with general settings
        $all_settings = array_merge($api_settings, $settings);

        // Save each setting individually and check for errors
        $saved_count = 0;
        $failed_settings = array();

        foreach ($all_settings as $option => $value) {
            $old_value = get_option($option);
            $result = update_option($option, $value);

            // Verify the setting was actually saved by reading it back
            $saved_value = get_option($option);
            $actually_saved = ($saved_value === $value);

            if ($actually_saved) {
                $saved_count++;
            } else {
                $failed_settings[] = $option;
            }

            // Debug logging for critical settings
            if (in_array($option, ['openai_chatbot_api_key', 'openai_chatbot_assistant_id']) && defined('WP_DEBUG') && WP_DEBUG) {
                $masked_value = $option === 'openai_chatbot_api_key' ? (empty($value) ? 'empty' : 'sk-***') : $value;
                $masked_saved = $option === 'openai_chatbot_api_key' ? (empty($saved_value) ? 'empty' : 'sk-***') : $saved_value;
                error_log("OpenAI Chatbot: Saving {$option} = {$masked_value}, result = " . ($result ? 'success' : 'failed') . ", verified = " . ($actually_saved ? 'yes' : 'no') . ", saved_value = {$masked_saved}");
            }

            // Throw exception if critical setting failed to save
            if (!$actually_saved && in_array($option, ['openai_chatbot_api_key', 'openai_chatbot_assistant_id'])) {
                throw new Exception(sprintf(__('Critical setting failed to save: %s (attempted: %s, actual: %s)', 'openai-chatbot'), $option, $value, $saved_value));
            }
        }

        // Log summary
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("OpenAI Chatbot: Settings save summary - {$saved_count}/" . count($all_settings) . " settings saved successfully");
            if (!empty($failed_settings)) {
                error_log("OpenAI Chatbot: Failed settings: " . implode(', ', $failed_settings));
            }
        }

        $settings_saved = true;

        // Clear any cached API instances to force reload with new settings
        wp_cache_delete('openai_chatbot_api_instance', 'openai_chatbot');

        // Clear any transients that might cache API configuration
        delete_transient('openai_chatbot_api_configured');
        delete_transient('openai_chatbot_assistant_configured');

        // Force clear all WordPress object cache to ensure settings are refreshed
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }

        // Clear specific option cache entries
        $settings_keys = [
            'openai_chatbot_enabled', 'openai_chatbot_bot_name', 'openai_chatbot_position',
            'openai_chatbot_theme', 'openai_chatbot_welcome_message', 'openai_chatbot_placeholder',
            'openai_chatbot_powered_by_text', 'openai_chatbot_quick_questions', 'openai_chatbot_show_timestamps',
            'openai_chatbot_max_message_length', 'openai_chatbot_primary_color', 'openai_chatbot_secondary_color',
            'openai_chatbot_accent_color', 'openai_chatbot_bot_text_color', 'openai_chatbot_user_text_color',
            'openai_chatbot_rate_limit_anonymous', 'openai_chatbot_rate_limit_logged_in',
            'openai_chatbot_rate_limit_global', 'openai_chatbot_rate_limit_burst'
        ];

        foreach ($settings_keys as $key) {
            wp_cache_delete($key, 'options');
        }

        // Clear settings cache to ensure frontend gets fresh data
        $plugin_instance = OpenAI_Chatbot_Plugin::get_instance();
        if (method_exists($plugin_instance, 'clear_settings_cache')) {
            $plugin_instance->clear_settings_cache();
        }
        
        // Additional cache clearing for persistent caching plugins
        if (function_exists('wp_cache_flush_group')) {
            wp_cache_flush_group('options');
        }
        
        // Clear any page caches that might be storing the old chatbot HTML
        if (function_exists('wp_cache_flush_runtime')) {
            wp_cache_flush_runtime();
        }
        
        // Clear opcache if available (for PHP file caching)
        if (function_exists('opcache_reset')) {
            opcache_reset();
        }

    } catch (Exception $e) {
        $error_message = $e->getMessage();
        error_log('OpenAI Chatbot Settings Save Error: ' . $error_message);
    }

    // Display appropriate message
    if ($settings_saved) {
        echo '<div class="notice notice-success is-dismissible"><p>' . __('Settings saved successfully!', 'openai-chatbot') . '</p></div>';
    } else {
        echo '<div class="notice notice-error is-dismissible"><p>' . __('Error saving settings: ', 'openai-chatbot') . esc_html($error_message) . '</p></div>';
    }
}

// Test database operations
if (isset($_POST['test_database']) && wp_verify_nonce($_POST['openai_chatbot_test_db_nonce'], 'openai_chatbot_test_db')) {
    $test_key = 'openai_chatbot_test_' . time();
    $test_value = 'test_value_' . wp_generate_uuid4();

    // Test write
    $write_result = update_option($test_key, $test_value);

    // Test read
    $read_value = get_option($test_key);

    // Test delete
    $delete_result = delete_option($test_key);

    if ($write_result && $read_value === $test_value && $delete_result) {
        echo '<div class="notice notice-success is-dismissible"><p>' . __('Database operations test successful!', 'openai-chatbot') . '</p></div>';
    } else {
        echo '<div class="notice notice-error is-dismissible"><p>' . sprintf(__('Database operations test failed! Write: %s, Read: %s, Delete: %s', 'openai-chatbot'),
            $write_result ? 'OK' : 'FAIL',
            $read_value === $test_value ? 'OK' : 'FAIL',
            $delete_result ? 'OK' : 'FAIL'
        ) . '</p></div>';
    }
}

// Test specific API settings save
if (isset($_POST['test_api_save']) && wp_verify_nonce($_POST['openai_chatbot_test_api_save_nonce'], 'openai_chatbot_test_api_save')) {
    $test_api_key = isset($_POST['test_api_key']) ? sanitize_text_field($_POST['test_api_key']) : '';
    $test_assistant_id = isset($_POST['test_assistant_id']) ? sanitize_text_field($_POST['test_assistant_id']) : '';

    $api_result = update_option('openai_chatbot_api_key', $test_api_key);
    $assistant_result = update_option('openai_chatbot_assistant_id', $test_assistant_id);

    $api_verify = get_option('openai_chatbot_api_key');
    $assistant_verify = get_option('openai_chatbot_assistant_id');

    if ($api_result && $assistant_result && $api_verify === $test_api_key && $assistant_verify === $test_assistant_id) {
        echo '<div class="notice notice-success is-dismissible"><p>' . __('API settings test save successful!', 'openai-chatbot') . '</p></div>';
    } else {
        echo '<div class="notice notice-error is-dismissible"><p>' . sprintf(__('API settings test save failed! API Key: %s (%s), Assistant ID: %s (%s)', 'openai-chatbot'),
            $api_result ? 'OK' : 'FAIL',
            $api_verify === $test_api_key ? 'verified' : 'mismatch',
            $assistant_result ? 'OK' : 'FAIL',
            $assistant_verify === $test_assistant_id ? 'verified' : 'mismatch'
        ) . '</p></div>';
    }
}

// Test API connection
if (isset($_POST['test_api']) && wp_verify_nonce($_POST['openai_chatbot_test_nonce'], 'openai_chatbot_test')) {
    try {
        // Create a fresh API handler instance to test current settings
        $api_handler = new OpenAI_Chatbot_API();
        $test_result = $api_handler->test_connection();

        if ($test_result['success']) {
            echo '<div class="notice notice-success is-dismissible"><p>' . __('API connection successful!', 'openai-chatbot') . '</p></div>';
        } else {
            echo '<div class="notice notice-error is-dismissible"><p>' . __('API connection failed: ', 'openai-chatbot') . esc_html($test_result['message']) . '</p></div>';
        }
    } catch (Exception $e) {
        echo '<div class="notice notice-error is-dismissible"><p>' . __('API connection failed: ', 'openai-chatbot') . esc_html($e->getMessage()) . '</p></div>';
    }
}

// Get current API configuration settings with proper fallbacks
$api_key = get_option('openai_chatbot_api_key', '');
if (empty($api_key) && defined('OPENAI_API_KEY')) {
    $api_key = OPENAI_API_KEY;
}

$assistant_id = get_option('openai_chatbot_assistant_id', '');
if (empty($assistant_id) && defined('OPENAI_ASSISTANT_ID')) {
    $assistant_id = OPENAI_ASSISTANT_ID;
}

$system_instructions = get_option('openai_chatbot_system_instructions', '');
if (empty($system_instructions)) {
    $system_instructions = sprintf(
        __('You are a helpful assistant for %s. Be concise, friendly, and helpful. If you don\'t know something, say so honestly.', 'openai-chatbot'),
        get_bloginfo('name')
    );
}

$model_name = get_option('openai_chatbot_model_name', 'gpt-4o');
$tools_enabled = get_option('openai_chatbot_tools_enabled', array('file_search', 'code_interpreter'));
// Ensure tools_enabled is always an array
if (!is_array($tools_enabled)) {
    $tools_enabled = array();
}

$custom_tools = get_option('openai_chatbot_custom_tools', '');

// Get current general settings with proper type handling
$enabled = (bool) get_option('openai_chatbot_enabled', 1);
$position = get_option('openai_chatbot_position', 'bottom-right');
$theme = get_option('openai_chatbot_theme', 'default');
$bot_name = get_option('openai_chatbot_bot_name', __('AI Assistant', 'openai-chatbot'));
$welcome_message = get_option('openai_chatbot_welcome_message', __('Hello! How can I help you today?', 'openai-chatbot'));
$placeholder = get_option('openai_chatbot_placeholder', __('Type your message...', 'openai-chatbot'));
$powered_by_text = get_option('openai_chatbot_powered_by_text', __('Powered by OpenAI', 'openai-chatbot'));
$show_timestamps = (bool) get_option('openai_chatbot_show_timestamps', 0);
$max_message_length = (int) get_option('openai_chatbot_max_message_length', 1000);
$max_messages = (int) get_option('openai_chatbot_max_messages', 50);
$disabled_on = get_option('openai_chatbot_disabled_on', array());
// Ensure disabled_on is always an array
if (!is_array($disabled_on)) {
    $disabled_on = array();
}

$custom_css = get_option('openai_chatbot_custom_css', '');
$add_structured_data = (bool) get_option('openai_chatbot_add_structured_data', 0);
$primary_color = get_option('openai_chatbot_primary_color', '#e74266');
$secondary_color = get_option('openai_chatbot_secondary_color', '#cf3c5c');
$accent_color = get_option('openai_chatbot_accent_color', '#1ca08a');
$bot_text_color = get_option('openai_chatbot_bot_text_color', '#1e1e1e');
$user_text_color = get_option('openai_chatbot_user_text_color', '#ffffff');
$quick_questions = get_option('openai_chatbot_quick_questions', '');

// Check API configuration (prioritize database settings over wp-config)
$api_configured = !empty($api_key) || (defined('OPENAI_API_KEY') && !empty(OPENAI_API_KEY));
$assistant_configured = !empty($assistant_id) || (defined('OPENAI_ASSISTANT_ID') && !empty(OPENAI_ASSISTANT_ID));

// Available OpenAI models
$available_models = array(
    'gpt-4.1' => 'GPT-4.1',
    'gpt-4.1-2025-04-14' => 'GPT-4.1 (2025-04-14)',
    'gpt-4.1-mini' => 'GPT-4.1 Mini',
    'gpt-4.1-mini-2025-04-14' => 'GPT-4.1 Mini (2025-04-14)',
    'gpt-4.1-nano' => 'GPT-4.1 Nano',
    'gpt-4.1-nano-2025-04-14' => 'GPT-4.1 Nano (2025-04-14)',
    'gpt-4.5-preview' => 'GPT-4.5 Preview',
    'gpt-4.5-preview-2025-02-27' => 'GPT-4.5 Preview (2025-02-27)',
    'gpt-4o' => 'GPT-4o',
    'gpt-4o-2024-08-06' => 'GPT-4o (2024-08-06)',
    'gpt-4o-audio-preview' => 'GPT-4o Audio Preview',
    'gpt-4o-audio-preview-2024-12-17' => 'GPT-4o Audio Preview (2024-12-17)',
    'gpt-4o-realtime-preview' => 'GPT-4o Realtime Preview',
    'gpt-4o-realtime-preview-2024-12-17' => 'GPT-4o Realtime Preview (2024-12-17)',
    'gpt-4o-mini' => 'GPT-4o Mini',
    'gpt-4o-mini-2024-07-18' => 'GPT-4o Mini (2024-07-18)',
    'gpt-4o-mini-audio-preview' => 'GPT-4o Mini Audio Preview',
    'gpt-4o-mini-audio-preview-2024-12-17' => 'GPT-4o Mini Audio Preview (2024-12-17)',
    'gpt-4o-mini-realtime-preview' => 'GPT-4o Mini Realtime Preview',
    'gpt-4o-mini-realtime-preview-2024-12-17' => 'GPT-4o Mini Realtime Preview (2024-12-17)'
);

// Available tools
$available_tools = array(
    'file_search' => __('File Search', 'openai-chatbot'),
    'code_interpreter' => __('Code Interpreter', 'openai-chatbot'),
    'function_calling' => __('Function Calling', 'openai-chatbot')
);
?>

<div class="wrap">
    <h1><?php esc_html_e('OpenAI Chatbot Settings', 'openai-chatbot'); ?></h1>
    
    <?php if (!$api_configured || !$assistant_configured): ?>
        <div class="notice notice-warning">
            <p><strong><?php esc_html_e('Configuration Required', 'openai-chatbot'); ?></strong></p>
            <?php if (!$api_configured): ?>
                <p><?php esc_html_e('Please configure your OpenAI API key in the API Configuration section below, or add it to wp-config.php:', 'openai-chatbot'); ?></p>
                <code>define('OPENAI_API_KEY', 'your-api-key-here');</code>
            <?php endif; ?>
            <?php if (!$assistant_configured): ?>
                <p><?php esc_html_e('Please configure your OpenAI Assistant ID in the API Configuration section below, or add it to wp-config.php:', 'openai-chatbot'); ?></p>
                <code>define('OPENAI_ASSISTANT_ID', 'your-assistant-id-here');</code>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- Debug Information (only show if WP_DEBUG is enabled) -->
    <?php if (defined('WP_DEBUG') && WP_DEBUG): ?>
        <div class="notice notice-info">
            <p><strong><?php esc_html_e('Debug Information (WP_DEBUG enabled)', 'openai-chatbot'); ?></strong></p>
            <p>
                <?php esc_html_e('API Key in database:', 'openai-chatbot'); ?>
                <code><?php echo !empty(get_option('openai_chatbot_api_key', '')) ? 'Present (' . substr(get_option('openai_chatbot_api_key', ''), 0, 7) . '...)' : 'Empty'; ?></code>
            </p>
            <p>
                <?php esc_html_e('Assistant ID in database:', 'openai-chatbot'); ?>
                <code><?php echo !empty(get_option('openai_chatbot_assistant_id', '')) ? 'Present (' . get_option('openai_chatbot_assistant_id', '') . ')' : 'Empty'; ?></code>
            </p>
            <p>
                <?php esc_html_e('Form submission method:', 'openai-chatbot'); ?>
                <code><?php echo $_SERVER['REQUEST_METHOD']; ?></code>
            </p>
            <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
                <p>
                    <?php esc_html_e('Form submission detected:', 'openai-chatbot'); ?>
                    <code><?php
                        $form_submitted = (isset($_POST['submit']) || isset($_POST['submit_button']) || (isset($_POST['openai_chatbot_settings_nonce']) && $_SERVER['REQUEST_METHOD'] === 'POST'));
                        echo $form_submitted ? 'Yes' : 'No';
                    ?></code>
                </p>
                <p>
                    <?php esc_html_e('POST nonce present:', 'openai-chatbot'); ?>
                    <code><?php echo isset($_POST['openai_chatbot_settings_nonce']) ? 'Yes' : 'No'; ?></code>
                </p>
                <?php if (isset($_POST['openai_chatbot_settings_nonce'])): ?>
                    <p>
                        <?php esc_html_e('Nonce verification:', 'openai-chatbot'); ?>
                        <code><?php echo wp_verify_nonce($_POST['openai_chatbot_settings_nonce'], 'openai_chatbot_settings') ? 'PASSED' : 'FAILED'; ?></code>
                    </p>
                <?php endif; ?>
                <p>
                    <?php esc_html_e('POST API Key present:', 'openai-chatbot'); ?>
                    <code><?php echo isset($_POST['openai_chatbot_api_key']) ? 'Yes' : 'No'; ?></code>
                </p>
                <p>
                    <?php esc_html_e('POST Assistant ID present:', 'openai-chatbot'); ?>
                    <code><?php echo isset($_POST['openai_chatbot_assistant_id']) ? 'Yes' : 'No'; ?></code>
                </p>
                <p>
                    <?php esc_html_e('All POST keys:', 'openai-chatbot'); ?>
                    <code><?php echo implode(', ', array_keys($_POST)); ?></code>
                </p>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <div class="openai-chatbot-admin">
        <div class="admin-content">
            <!-- Main Settings Form -->
            <form method="post" action="">
                <?php wp_nonce_field('openai_chatbot_settings', 'openai_chatbot_settings_nonce'); ?>

                <!-- API Configuration -->
                <div class="postbox">
                    <div class="postbox-header">
                        <h2 class="hndle"><?php esc_html_e('API Configuration', 'openai-chatbot'); ?></h2>
                    </div>
                    <div class="inside">
                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php esc_html_e('OpenAI API Key', 'openai-chatbot'); ?></th>
                                <td>
                                    <input type="password" name="openai_chatbot_api_key" value="<?php echo esc_attr($api_key); ?>" class="regular-text" autocomplete="new-password">
                                    <button type="button" class="button button-secondary toggle-password" data-target="openai_chatbot_api_key"><?php esc_html_e('Show', 'openai-chatbot'); ?></button>
                                    <p class="description">
                                        <?php esc_html_e('Your OpenAI API key. This will override any key set in wp-config.php.', 'openai-chatbot'); ?>
                                        <br><a href="https://platform.openai.com/api-keys" target="_blank"><?php esc_html_e('Get your API key from OpenAI', 'openai-chatbot'); ?></a>
                                    </p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Assistant ID', 'openai-chatbot'); ?></th>
                                <td>
                                    <input type="text" name="openai_chatbot_assistant_id" value="<?php echo esc_attr($assistant_id); ?>" class="regular-text">
                                    <p class="description">
                                        <?php esc_html_e('Your OpenAI Assistant ID. This will override any ID set in wp-config.php.', 'openai-chatbot'); ?>
                                        <br><a href="https://platform.openai.com/assistants" target="_blank"><?php esc_html_e('Create and manage assistants on OpenAI', 'openai-chatbot'); ?></a>
                                    </p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Model Name', 'openai-chatbot'); ?></th>
                                <td>
                                    <select name="openai_chatbot_model_name" class="regular-text">
                                        <?php foreach ($available_models as $model_key => $model_label): ?>
                                            <option value="<?php echo esc_attr($model_key); ?>" <?php selected($model_name, $model_key); ?>>
                                                <?php echo esc_html($model_label); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <p class="description"><?php esc_html_e('Select the OpenAI model to use for your assistant.', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('System Instructions', 'openai-chatbot'); ?></th>
                                <td>
                                    <textarea name="openai_chatbot_system_instructions" rows="8" class="large-text"><?php echo esc_textarea($system_instructions); ?></textarea>
                                    <p class="description"><?php esc_html_e('System instructions that define how your assistant should behave and respond to users.', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Tools Configuration -->
                <div class="postbox">
                    <div class="postbox-header">
                        <h2 class="hndle"><?php esc_html_e('Tools Configuration', 'openai-chatbot'); ?></h2>
                    </div>
                    <div class="inside">
                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php esc_html_e('Available Tools', 'openai-chatbot'); ?></th>
                                <td>
                                    <fieldset>
                                        <?php foreach ($available_tools as $tool_key => $tool_label): ?>
                                            <label>
                                                <input type="checkbox" name="openai_chatbot_tools_enabled[]" value="<?php echo esc_attr($tool_key); ?>" <?php checked(in_array($tool_key, $tools_enabled)); ?>>
                                                <?php echo esc_html($tool_label); ?>
                                            </label><br>
                                        <?php endforeach; ?>
                                    </fieldset>
                                    <p class="description"><?php esc_html_e('Select which tools your assistant can use to enhance its capabilities.', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Custom Functions', 'openai-chatbot'); ?></th>
                                <td>
                                    <textarea name="openai_chatbot_custom_tools" rows="10" class="large-text code"><?php echo esc_textarea($custom_tools); ?></textarea>
                                    <p class="description">
                                        <?php esc_html_e('Define custom functions in JSON format for your assistant to use. Advanced users only.', 'openai-chatbot'); ?>
                                        <br><a href="https://platform.openai.com/docs/assistants/tools/function-calling" target="_blank"><?php esc_html_e('Learn about function calling', 'openai-chatbot'); ?></a>
                                    </p>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- General Settings -->
                <div class="postbox">
                    <div class="postbox-header">
                        <h2 class="hndle"><?php esc_html_e('General Settings', 'openai-chatbot'); ?></h2>
                    </div>
                    <div class="inside">
                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php esc_html_e('Enable Chatbot', 'openai-chatbot'); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="openai_chatbot_enabled" value="1" <?php checked($enabled); ?>>
                                        <?php esc_html_e('Enable the chatbot on your website', 'openai-chatbot'); ?>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Bot Name', 'openai-chatbot'); ?></th>
                                <td>
                                    <input type="text" name="openai_chatbot_bot_name" value="<?php echo esc_attr($bot_name); ?>" class="regular-text">
                                    <p class="description"><?php esc_html_e('The name displayed in the chat header', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Welcome Message', 'openai-chatbot'); ?></th>
                                <td>
                                    <textarea name="openai_chatbot_welcome_message" rows="3" class="large-text"><?php echo esc_textarea($welcome_message); ?></textarea>
                                    <p class="description"><?php esc_html_e('The first message users see when opening the chat', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Input Placeholder', 'openai-chatbot'); ?></th>
                                <td>
                                    <input type="text" name="openai_chatbot_placeholder" value="<?php echo esc_attr($placeholder); ?>" class="regular-text">
                                    <p class="description"><?php esc_html_e('Placeholder text in the message input field', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Powered By Text', 'openai-chatbot'); ?></th>
                                <td>
                                    <input type="text" name="openai_chatbot_powered_by_text" value="<?php echo esc_attr($powered_by_text); ?>" class="regular-text">
                                    <p class="description"><?php esc_html_e('Text displayed in the chat footer', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Quick Questions', 'openai-chatbot'); ?></th>
                                <td>
                                    <textarea name="openai_chatbot_quick_questions" rows="6" class="large-text" placeholder="<?php esc_attr_e('Enter one question per line, e.g.:\nWhat products do you offer?\nHow can I contact support?\nWhat are your business hours?', 'openai-chatbot'); ?>"><?php echo esc_textarea($quick_questions); ?></textarea>
                                    <p class="description"><?php esc_html_e('Pre-defined questions that users can click to quickly start conversations. Enter one question per line.', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Appearance Settings -->
                <div class="postbox">
                    <div class="postbox-header">
                        <h2 class="hndle"><?php esc_html_e('Appearance', 'openai-chatbot'); ?></h2>
                    </div>
                    <div class="inside">
                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php esc_html_e('Position', 'openai-chatbot'); ?></th>
                                <td>
                                    <select name="openai_chatbot_position">
                                        <option value="bottom-right" <?php selected($position, 'bottom-right'); ?>><?php esc_html_e('Bottom Right', 'openai-chatbot'); ?></option>
                                        <option value="bottom-left" <?php selected($position, 'bottom-left'); ?>><?php esc_html_e('Bottom Left', 'openai-chatbot'); ?></option>
                                        <option value="top-right" <?php selected($position, 'top-right'); ?>><?php esc_html_e('Top Right', 'openai-chatbot'); ?></option>
                                        <option value="top-left" <?php selected($position, 'top-left'); ?>><?php esc_html_e('Top Left', 'openai-chatbot'); ?></option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Theme', 'openai-chatbot'); ?></th>
                                <td>
                                    <select name="openai_chatbot_theme">
                                        <option value="default" <?php selected($theme, 'default'); ?>><?php esc_html_e('Default', 'openai-chatbot'); ?></option>
                                        <option value="dark" <?php selected($theme, 'dark'); ?>><?php esc_html_e('Dark', 'openai-chatbot'); ?></option>
                                        <option value="minimal" <?php selected($theme, 'minimal'); ?>><?php esc_html_e('Minimal', 'openai-chatbot'); ?></option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Show Timestamps', 'openai-chatbot'); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="openai_chatbot_show_timestamps" value="1" <?php checked($show_timestamps); ?>>
                                        <?php esc_html_e('Display timestamps on messages', 'openai-chatbot'); ?>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Theme Colors', 'openai-chatbot'); ?></th>
                                <td>
                                    <div class="color-picker-section">
                                        <div class="color-picker-row">
                                            <label for="openai_chatbot_primary_color"><?php esc_html_e('Primary Color:', 'openai-chatbot'); ?></label>
                                            <input type="text" id="openai_chatbot_primary_color" name="openai_chatbot_primary_color" value="<?php echo esc_attr(get_option('openai_chatbot_primary_color', '#e74266')); ?>" class="color-picker" data-default-color="#e74266">
                                            <span class="color-description"><?php esc_html_e('Main theme color for buttons and accents', 'openai-chatbot'); ?></span>
                                        </div>
                                        <div class="color-picker-row">
                                            <label for="openai_chatbot_secondary_color"><?php esc_html_e('Secondary Color:', 'openai-chatbot'); ?></label>
                                            <input type="text" id="openai_chatbot_secondary_color" name="openai_chatbot_secondary_color" value="<?php echo esc_attr(get_option('openai_chatbot_secondary_color', '#cf3c5c')); ?>" class="color-picker" data-default-color="#cf3c5c">
                                            <span class="color-description"><?php esc_html_e('Secondary color for hover states and highlights', 'openai-chatbot'); ?></span>
                                        </div>
                                        <div class="color-picker-row">
                                            <label for="openai_chatbot_accent_color"><?php esc_html_e('Accent Color:', 'openai-chatbot'); ?></label>
                                            <input type="text" id="openai_chatbot_accent_color" name="openai_chatbot_accent_color" value="<?php echo esc_attr(get_option('openai_chatbot_accent_color', '#1ca08a')); ?>" class="color-picker" data-default-color="#1ca08a">
                                            <span class="color-description"><?php esc_html_e('Accent color for special elements and indicators', 'openai-chatbot'); ?></span>
                                        </div>
                                        <div class="color-picker-row">
                                            <label for="openai_chatbot_bot_text_color"><?php esc_html_e('Bot Message Text:', 'openai-chatbot'); ?></label>
                                            <input type="text" id="openai_chatbot_bot_text_color" name="openai_chatbot_bot_text_color" value="<?php echo esc_attr($bot_text_color); ?>" class="color-picker" data-default-color="#1e1e1e">
                                            <span class="color-description"><?php esc_html_e('Text color for bot messages', 'openai-chatbot'); ?></span>
                                        </div>
                                        <div class="color-picker-row">
                                            <label for="openai_chatbot_user_text_color"><?php esc_html_e('User Message Text:', 'openai-chatbot'); ?></label>
                                            <input type="text" id="openai_chatbot_user_text_color" name="openai_chatbot_user_text_color" value="<?php echo esc_attr($user_text_color); ?>" class="color-picker" data-default-color="#ffffff">
                                            <span class="color-description"><?php esc_html_e('Text color for user messages', 'openai-chatbot'); ?></span>
                                        </div>
                                        <div class="color-preview-section">
                                            <h4><?php esc_html_e('Preview:', 'openai-chatbot'); ?></h4>
                                            <div class="theme-preview">
                                                <div class="preview-chatbot-widget">
                                                    <div class="preview-header">
                                                        <div class="preview-bot-name"><?php esc_html_e('AI Assistant', 'openai-chatbot'); ?></div>
                                                        <div class="preview-status"><?php esc_html_e('Online', 'openai-chatbot'); ?></div>
                                                    </div>
                                                    <div class="preview-messages">
                                                        <div class="preview-message bot-preview">
                                                            <div class="preview-content bot-content"><?php esc_html_e('Hello! How can I help you today?', 'openai-chatbot'); ?></div>
                                                        </div>
                                                        <div class="preview-quick-questions">
                                                            <button class="preview-quick-btn"><?php esc_html_e('What products do you offer?', 'openai-chatbot'); ?></button>
                                                        </div>
                                                        <div class="preview-message user-preview">
                                                            <div class="preview-content user-content"><?php esc_html_e('This is a user message', 'openai-chatbot'); ?></div>
                                                        </div>
                                                    </div>
                                                    <div class="preview-input">
                                                        <div class="preview-input-field"><?php esc_html_e('Type your message...', 'openai-chatbot'); ?></div>
                                                        <div class="preview-send-btn"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="color-actions">
                                                <button type="button" class="button button-secondary reset-colors"><?php esc_html_e('Reset to Default Colors', 'openai-chatbot'); ?></button>
                                                <button type="button" class="button button-secondary preview-colors"><?php esc_html_e('Preview Changes', 'openai-chatbot'); ?></button>
                                            </div>
                                        </div>
                                    </div>
                                    <p class="description"><?php esc_html_e('Customize the overall theme colors for your chatbot. Colors are automatically checked for accessibility contrast and applied throughout the interface.', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Custom CSS', 'openai-chatbot'); ?></th>
                                <td>
                                    <textarea name="openai_chatbot_custom_css" rows="10" class="large-text code"><?php echo esc_textarea($custom_css); ?></textarea>
                                    <p class="description"><?php esc_html_e('Add custom CSS to style the chatbot (advanced users only)', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Behavior Settings -->
                <div class="postbox">
                    <div class="postbox-header">
                        <h2 class="hndle"><?php esc_html_e('Behavior', 'openai-chatbot'); ?></h2>
                    </div>
                    <div class="inside">
                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php esc_html_e('Max Message Length', 'openai-chatbot'); ?></th>
                                <td>
                                    <input type="number" name="openai_chatbot_max_message_length" value="<?php echo esc_attr($max_message_length); ?>" min="100" max="2000" class="small-text">
                                    <p class="description"><?php esc_html_e('Maximum characters allowed per message', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Max Messages in History', 'openai-chatbot'); ?></th>
                                <td>
                                    <input type="number" name="openai_chatbot_max_messages" value="<?php echo esc_attr($max_messages); ?>" min="10" max="200" class="small-text">
                                    <p class="description"><?php esc_html_e('Maximum messages to keep in conversation history', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Disable On', 'openai-chatbot'); ?></th>
                                <td>
                                    <fieldset>
                                        <label>
                                            <input type="checkbox" name="openai_chatbot_disabled_on[]" value="home" <?php checked(in_array('home', $disabled_on)); ?>>
                                            <?php esc_html_e('Home Page', 'openai-chatbot'); ?>
                                        </label><br>
                                        <label>
                                            <input type="checkbox" name="openai_chatbot_disabled_on[]" value="posts" <?php checked(in_array('posts', $disabled_on)); ?>>
                                            <?php esc_html_e('Blog Posts', 'openai-chatbot'); ?>
                                        </label><br>
                                        <label>
                                            <input type="checkbox" name="openai_chatbot_disabled_on[]" value="pages" <?php checked(in_array('pages', $disabled_on)); ?>>
                                            <?php esc_html_e('Pages', 'openai-chatbot'); ?>
                                        </label><br>
                                        <label>
                                            <input type="checkbox" name="openai_chatbot_disabled_on[]" value="archive" <?php checked(in_array('archive', $disabled_on)); ?>>
                                            <?php esc_html_e('Archive Pages', 'openai-chatbot'); ?>
                                        </label>
                                    </fieldset>
                                    <p class="description"><?php esc_html_e('Select page types where the chatbot should be disabled', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>



                <!-- Advanced Settings -->
                <div class="postbox">
                    <div class="postbox-header">
                        <h2 class="hndle"><?php esc_html_e('Advanced', 'openai-chatbot'); ?></h2>
                    </div>
                    <div class="inside">
                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php esc_html_e('Structured Data', 'openai-chatbot'); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="openai_chatbot_add_structured_data" value="1" <?php checked($add_structured_data); ?>>
                                        <?php esc_html_e('Add structured data for SEO', 'openai-chatbot'); ?>
                                    </label>
                                    <p class="description"><?php esc_html_e('Adds JSON-LD structured data to help search engines understand your chatbot', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <?php submit_button(__('Save Settings', 'openai-chatbot')); ?>
            </form>
        </div>

        <!-- Sidebar -->
        <div class="admin-sidebar">
            <!-- Database Test -->
            <div class="postbox">
                <div class="postbox-header">
                    <h2 class="hndle"><?php esc_html_e('Database Test', 'openai-chatbot'); ?></h2>
                </div>
                <div class="inside">
                    <p><?php esc_html_e('Test database operations to ensure settings can be saved.', 'openai-chatbot'); ?></p>
                    <form method="post" action="">
                        <?php wp_nonce_field('openai_chatbot_test_db', 'openai_chatbot_test_db_nonce'); ?>
                        <input type="submit" name="test_database" class="button button-secondary" value="<?php esc_attr_e('Test Database', 'openai-chatbot'); ?>">
                    </form>
                </div>
            </div>

            <!-- API Settings Test -->
            <div class="postbox">
                <div class="postbox-header">
                    <h2 class="hndle"><?php esc_html_e('API Settings Test', 'openai-chatbot'); ?></h2>
                </div>
                <div class="inside">
                    <p><?php esc_html_e('Test saving API key and Assistant ID directly.', 'openai-chatbot'); ?></p>
                    <form method="post" action="">
                        <?php wp_nonce_field('openai_chatbot_test_api_save', 'openai_chatbot_test_api_save_nonce'); ?>
                        <p>
                            <label><?php esc_html_e('Test API Key:', 'openai-chatbot'); ?></label><br>
                            <input type="text" name="test_api_key" value="sk-test123" class="widefat">
                        </p>
                        <p>
                            <label><?php esc_html_e('Test Assistant ID:', 'openai-chatbot'); ?></label><br>
                            <input type="text" name="test_assistant_id" value="asst_test123" class="widefat">
                        </p>
                        <input type="submit" name="test_api_save" class="button button-secondary" value="<?php esc_attr_e('Test API Save', 'openai-chatbot'); ?>">
                    </form>
                </div>
            </div>

            <!-- API Test -->
            <div class="postbox">
                <div class="postbox-header">
                    <h2 class="hndle"><?php esc_html_e('API Connection Test', 'openai-chatbot'); ?></h2>
                </div>
                <div class="inside">
                    <p><?php esc_html_e('Test your OpenAI API connection to ensure everything is working correctly.', 'openai-chatbot'); ?></p>
                    <form method="post" action="">
                        <?php wp_nonce_field('openai_chatbot_test', 'openai_chatbot_test_nonce'); ?>
                        <input type="submit" name="test_api" class="button button-secondary" value="<?php esc_attr_e('Test API Connection', 'openai-chatbot'); ?>">
                    </form>
                </div>
            </div>

            <!-- Usage Instructions -->
            <div class="postbox">
                <div class="postbox-header">
                    <h2 class="hndle"><?php esc_html_e('Usage Instructions', 'openai-chatbot'); ?></h2>
                </div>
                <div class="inside">
                    <h4><?php esc_html_e('Shortcode', 'openai-chatbot'); ?></h4>
                    <p><?php esc_html_e('Use this shortcode to embed the chatbot inline:', 'openai-chatbot'); ?></p>
                    <code>[openai_chatbot]</code>
                    
                    <h4><?php esc_html_e('Shortcode Options', 'openai-chatbot'); ?></h4>
                    <ul>
                        <li><code>height="400px"</code> - <?php esc_html_e('Set height', 'openai-chatbot'); ?></li>
                        <li><code>width="100%"</code> - <?php esc_html_e('Set width', 'openai-chatbot'); ?></li>
                    </ul>
                    
                    <h4><?php esc_html_e('Example', 'openai-chatbot'); ?></h4>
                    <code>[openai_chatbot height="500px" width="80%"]</code>
                </div>
            </div>



            <!-- Configuration Status -->
            <div class="postbox">
                <div class="postbox-header">
                    <h2 class="hndle"><?php esc_html_e('Configuration Status', 'openai-chatbot'); ?></h2>
                </div>
                <div class="inside">
                    <table class="widefat">
                        <tr>
                            <td><?php esc_html_e('API Key Source', 'openai-chatbot'); ?></td>
                            <td><strong>
                                <?php
                                $db_api_key = get_option('openai_chatbot_api_key', '');
                                if (!empty($db_api_key)) {
                                    echo esc_html__('Database', 'openai-chatbot');
                                } elseif (defined('OPENAI_API_KEY') && !empty(OPENAI_API_KEY)) {
                                    echo esc_html__('wp-config.php', 'openai-chatbot');
                                } else {
                                    echo esc_html__('Not configured', 'openai-chatbot');
                                }
                                ?>
                            </strong></td>
                        </tr>
                        <tr>
                            <td><?php esc_html_e('Assistant ID Source', 'openai-chatbot'); ?></td>
                            <td><strong>
                                <?php
                                $db_assistant_id = get_option('openai_chatbot_assistant_id', '');
                                if (!empty($db_assistant_id)) {
                                    echo esc_html__('Database', 'openai-chatbot');
                                } elseif (defined('OPENAI_ASSISTANT_ID') && !empty(OPENAI_ASSISTANT_ID)) {
                                    echo esc_html__('wp-config.php', 'openai-chatbot');
                                } else {
                                    echo esc_html__('Not configured', 'openai-chatbot');
                                }
                                ?>
                            </strong></td>
                        </tr>
                        <tr>
                            <td><?php esc_html_e('API Key Status', 'openai-chatbot'); ?></td>
                            <td><strong>
                                <?php
                                if (!empty($api_key)) {
                                    echo esc_html__('Configured', 'openai-chatbot');
                                } else {
                                    echo '<span style="color: #d63638;">' . esc_html__('Missing', 'openai-chatbot') . '</span>';
                                }
                                ?>
                            </strong></td>
                        </tr>
                        <tr>
                            <td><?php esc_html_e('Assistant ID Status', 'openai-chatbot'); ?></td>
                            <td><strong>
                                <?php
                                if (!empty($assistant_id)) {
                                    echo esc_html__('Configured', 'openai-chatbot');
                                } else {
                                    echo '<span style="color: #d63638;">' . esc_html__('Missing', 'openai-chatbot') . '</span>';
                                }
                                ?>
                            </strong></td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Support -->
            <div class="postbox">
                <div class="postbox-header">
                    <h2 class="hndle"><?php esc_html_e('Support', 'openai-chatbot'); ?></h2>
                </div>
                <div class="inside">
                    <p><?php esc_html_e('Need help? Check out these resources:', 'openai-chatbot'); ?></p>
                    <ul>
                        <li><a href="#" target="_blank"><?php esc_html_e('Documentation', 'openai-chatbot'); ?></a></li>
                        <li><a href="#" target="_blank"><?php esc_html_e('Support Forum', 'openai-chatbot'); ?></a></li>
                        <li><a href="https://platform.openai.com/docs" target="_blank"><?php esc_html_e('OpenAI API Docs', 'openai-chatbot'); ?></a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.openai-chatbot-admin {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.admin-content {
    flex: 2;
}

.admin-sidebar {
    flex: 1;
    max-width: 300px;
}

.admin-sidebar .postbox {
    margin-bottom: 20px;
}

.admin-sidebar .inside ul {
    margin: 10px 0;
    padding-left: 20px;
}

.admin-sidebar .inside li {
    margin-bottom: 5px;
}

.admin-sidebar code {
    background: #f1f1f1;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.admin-sidebar table.widefat td {
    padding: 8px 10px;
}

/* Color Picker Styles */
.color-picker-section {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #ddd;
}

.color-picker-row {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 15px;
    flex-wrap: wrap;
}

.color-picker-row label {
    min-width: 160px;
    font-weight: 600;
    color: #333;
}

.color-description {
    font-size: 12px;
    color: #666;
    font-style: italic;
    margin-left: 10px;
}

.color-preview-section {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
}

.theme-preview {
    background: #f0f0f0;
    padding: 20px;
    border-radius: 12px;
    margin: 15px 0;
}

.preview-chatbot-widget {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    max-width: 350px;
    margin: 0 auto;
}

.preview-header {
    background: var(--primary-color, #e74266);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preview-bot-name {
    font-weight: 600;
    font-size: 16px;
}

.preview-status {
    font-size: 12px;
    opacity: 0.9;
}

.preview-messages {
    padding: 20px;
    min-height: 200px;
}

.preview-message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
}

.user-preview {
    justify-content: flex-end;
}

.bot-preview {
    justify-content: flex-start;
}

.preview-content {
    padding: 10px 15px;
    border-radius: 18px;
    max-width: 70%;
    font-size: 14px;
    line-height: 1.4;
}

.user-content {
    background: var(--primary-color, #e74266);
    color: var(--user-text-color, #ffffff);
}

.bot-content {
    background: #f5f5f5;
    border: 1px solid #e0e0e0;
    color: var(--bot-text-color, #1e1e1e);
}

.preview-quick-questions {
    margin: 15px 0;
}

.preview-quick-btn {
    background: var(--accent-color, #1ca08a);
    color: white;
    border: none;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.preview-quick-btn:hover {
    background: var(--secondary-color, #cf3c5c);
    transform: translateY(-1px);
}

.preview-input {
    border-top: 1px solid #e0e0e0;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.preview-input-field {
    flex: 1;
    background: #f8f8f8;
    border: 1px solid #ddd;
    border-radius: 20px;
    padding: 10px 15px;
    font-size: 14px;
    color: #999;
}

.preview-send-btn {
    width: 36px;
    height: 36px;
    background: var(--primary-color, #e74266);
    border-radius: 50%;
    position: relative;
}

.preview-send-btn::after {
    content: '→';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
}

.color-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.reset-colors, .preview-colors {
    margin: 0;
}

/* API Configuration Styles */
.toggle-password {
    margin-left: 5px;
}

.form-table input[type="password"],
.form-table input[type="text"].regular-text {
    width: 400px;
    max-width: 100%;
}

.form-table textarea.large-text {
    width: 100%;
    max-width: 600px;
}

.form-table .code {
    font-family: Consolas, Monaco, monospace;
    font-size: 13px;
}

.postbox .inside {
    padding: 20px;
}

.postbox-header h2 {
    font-size: 16px;
    font-weight: 600;
}

/* Tools configuration */
.form-table fieldset label {
    display: block;
    margin-bottom: 8px;
}

.form-table fieldset input[type="checkbox"] {
    margin-right: 8px;
}

/* Error styling */
.form-table textarea.error {
    border-color: #d63638;
    box-shadow: 0 0 2px rgba(214, 54, 56, 0.8);
}

@media (max-width: 1200px) {
    .openai-chatbot-admin {
        flex-direction: column;
    }

    .admin-sidebar {
        max-width: none;
    }

    .color-picker-row {
        flex-direction: column;
        align-items: flex-start;
    }

    .color-picker-row label {
        min-width: auto;
        margin-bottom: 5px;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Password toggle functionality
    $('.toggle-password').on('click', function() {
        var target = $(this).data('target');
        var input = $('input[name="' + target + '"]');
        var button = $(this);

        if (input.attr('type') === 'password') {
            input.attr('type', 'text');
            button.text('<?php esc_html_e("Hide", "openai-chatbot"); ?>');
        } else {
            input.attr('type', 'password');
            button.text('<?php esc_html_e("Show", "openai-chatbot"); ?>');
        }
    });

    // Initialize WordPress color picker
    $('.color-picker').wpColorPicker({
        change: function(event, ui) {
            updateColorPreview();
        },
        clear: function() {
            updateColorPreview();
        }
    });

    // Update color preview
    function updateColorPreview() {
        var primaryColor = $('#openai_chatbot_primary_color').val() || '#e74266';
        var secondaryColor = $('#openai_chatbot_secondary_color').val() || '#cf3c5c';
        var accentColor = $('#openai_chatbot_accent_color').val() || '#1ca08a';
        var botColor = $('#openai_chatbot_bot_text_color').val() || '#1e1e1e';
        var userColor = $('#openai_chatbot_user_text_color').val() || '#ffffff';

        // Update preview elements
        $('.bot-content').css('color', botColor);
        $('.user-content').css({
            'color': userColor,
            'background': primaryColor
        });
        $('.preview-header').css('background', primaryColor);
        $('.preview-send-btn').css('background', primaryColor);
        $('.preview-quick-btn').css('background', accentColor);

        // Update CSS custom properties for real-time preview
        document.documentElement.style.setProperty('--primary-color', primaryColor);
        document.documentElement.style.setProperty('--secondary-color', secondaryColor);
        document.documentElement.style.setProperty('--accent-color', accentColor);
        document.documentElement.style.setProperty('--bot-text-color', botColor);
        document.documentElement.style.setProperty('--user-text-color', userColor);

        // Check contrast and show warnings if needed
        checkContrast(botColor, '#ffffff', 'bot'); // Bot text on white background
        checkContrast(userColor, primaryColor, 'user'); // User text on primary color background
        checkContrast('#ffffff', primaryColor, 'primary'); // White text on primary background
        checkContrast('#ffffff', accentColor, 'accent'); // White text on accent background
    }

    // Check color contrast for accessibility
    function checkContrast(textColor, bgColor, type) {
        var contrast = getContrastRatio(textColor, bgColor);
        var minContrast = 4.5; // WCAG AA standard

        var warningId = type + '-contrast-warning';
        $('#' + warningId).remove(); // Remove existing warning

        if (contrast < minContrast) {
            var message = type === 'bot' ?
                '<?php esc_html_e("Warning: Bot text color may not have sufficient contrast for accessibility.", "openai-chatbot"); ?>' :
                '<?php esc_html_e("Warning: User text color may not have sufficient contrast for accessibility.", "openai-chatbot"); ?>';

            var warning = '<div id="' + warningId + '" class="notice notice-warning inline" style="margin-top: 5px; padding: 5px 10px;"><p style="margin: 0; font-size: 12px;">' + message + ' (Contrast: ' + contrast.toFixed(2) + ':1, Recommended: ' + minContrast + ':1)</p></div>';
            $('#openai_chatbot_' + type + '_text_color').closest('.color-picker-row').append(warning);
        }
    }

    // Calculate contrast ratio between two colors
    function getContrastRatio(color1, color2) {
        var lum1 = getLuminance(color1);
        var lum2 = getLuminance(color2);
        var brightest = Math.max(lum1, lum2);
        var darkest = Math.min(lum1, lum2);
        return (brightest + 0.05) / (darkest + 0.05);
    }

    // Get relative luminance of a color
    function getLuminance(color) {
        var rgb = hexToRgb(color);
        if (!rgb) return 0;

        var rsRGB = rgb.r / 255;
        var gsRGB = rgb.g / 255;
        var bsRGB = rgb.b / 255;

        var r = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);
        var g = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);
        var b = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);

        return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    }

    // Convert hex color to RGB
    function hexToRgb(hex) {
        var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    // Reset colors to default
    $('.reset-colors').on('click', function() {
        $('#openai_chatbot_primary_color').wpColorPicker('color', '#e74266');
        $('#openai_chatbot_secondary_color').wpColorPicker('color', '#cf3c5c');
        $('#openai_chatbot_accent_color').wpColorPicker('color', '#1ca08a');
        $('#openai_chatbot_bot_text_color').wpColorPicker('color', '#1e1e1e');
        $('#openai_chatbot_user_text_color').wpColorPicker('color', '#ffffff');
        updateColorPreview();
    });

    // Preview colors button
    $('.preview-colors').on('click', function() {
        updateColorPreview();
        $(this).text('✓ Preview Updated').addClass('button-primary');
        setTimeout(function() {
            $('.preview-colors').text('Preview Changes').removeClass('button-primary');
        }, 2000);
    });

    // Form validation and debugging
    $('form').on('submit', function(e) {
        var form = $(this);
        var apiKey = $('input[name="openai_chatbot_api_key"]').val().trim();
        var assistantId = $('input[name="openai_chatbot_assistant_id"]').val().trim();

        // Debug: Log form submission
        console.log('OpenAI Chatbot: Form submission detected');
        console.log('API Key present:', apiKey ? 'yes' : 'no');
        console.log('Assistant ID present:', assistantId ? 'yes' : 'no');
        console.log('Form action:', form.attr('action'));
        console.log('Form method:', form.attr('method'));

        // Basic API key validation
        if (apiKey && !apiKey.startsWith('sk-')) {
            alert('<?php esc_html_e("API Key should start with 'sk-'", "openai-chatbot"); ?>');
            e.preventDefault();
            return false;
        }

        // Basic Assistant ID validation
        if (assistantId && !assistantId.startsWith('asst_')) {
            alert('<?php esc_html_e("Assistant ID should start with 'asst_'", "openai-chatbot"); ?>');
            e.preventDefault();
            return false;
        }

        // Debug: Log successful validation
        console.log('OpenAI Chatbot: Form validation passed, submitting...');
    });

    // JSON validation for custom tools
    $('textarea[name="openai_chatbot_custom_tools"]').on('blur', function() {
        var value = $(this).val().trim();
        if (value && value !== '') {
            try {
                JSON.parse(value);
                $(this).removeClass('error');
                $(this).next('.json-error').remove();
            } catch (e) {
                $(this).addClass('error');
                if (!$(this).next('.json-error').length) {
                    $(this).after('<p class="json-error" style="color: #d63638; margin-top: 5px;"><?php esc_html_e("Invalid JSON format", "openai-chatbot"); ?></p>');
                }
            }
        } else {
            $(this).removeClass('error');
            $(this).next('.json-error').remove();
        }
    });

    // Initial preview update
    updateColorPreview();
});
</script>