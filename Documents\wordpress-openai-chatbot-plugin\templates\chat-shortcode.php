<?php
/**
 * Chat Shortcode Template
 * 
 * This template renders the inline chat interface when using the [openai_chatbot] shortcode
 * It provides a different layout optimized for embedding within page content
 * 
 * @package OpenAI_Chatbot
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if API key is configured (database first, then wp-config)
$api_key = get_option('openai_chatbot_api_key', '');
if (empty($api_key) && defined('OPENAI_API_KEY')) {
    $api_key = OPENAI_API_KEY;
}

$assistant_id = get_option('openai_chatbot_assistant_id', '');
if (empty($assistant_id) && defined('OPENAI_ASSISTANT_ID')) {
    $assistant_id = OPENAI_ASSISTANT_ID;
}

if (empty($api_key) || empty($assistant_id)) {
    if (current_user_can('manage_options')) {
        return '<div class="openai-chatbot-error">' . __('OpenAI Chatbot: API key or Assistant ID not configured', 'openai-chatbot') . '</div>';
    }
    return '';
}

// Get shortcode attributes (passed from main plugin file)
$position = isset($atts['position']) ? $atts['position'] : 'inline';
$height = isset($atts['height']) ? $atts['height'] : '400px';
$width = isset($atts['width']) ? $atts['width'] : '100%';

// Get plugin options
$welcome_message = get_option('openai_chatbot_welcome_message', __('Hello! How can I help you today?', 'openai-chatbot'));
$placeholder = get_option('openai_chatbot_placeholder', __('Type your message...', 'openai-chatbot'));
$max_length = get_option('openai_chatbot_max_message_length', 1000);
$show_timestamps = get_option('openai_chatbot_show_timestamps', false);
$quick_questions = get_option('openai_chatbot_quick_questions', '');

// Generate unique ID for this instance
$instance_id = 'openai-chatbot-shortcode-' . wp_generate_password(8, false, false);
?>

<div id="<?php echo esc_attr($instance_id); ?>" class="openai-chatbot-shortcode" style="width: <?php echo esc_attr($width); ?>; height: <?php echo esc_attr($height); ?>;">
    <div class="chatbot-inline-container">
        <!-- Chat Header -->
        <div class="chatbot-inline-header">
            <div class="header-content">
                <div class="bot-avatar" aria-hidden="true">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                </div>
                <div class="bot-info">
                    <h3 class="bot-name">
                        <?php echo esc_html(get_option('openai_chatbot_bot_name', __('AI Assistant', 'openai-chatbot'))); ?>
                    </h3>
                    <p class="bot-status" aria-live="polite">
                        <?php esc_html_e('Online', 'openai-chatbot'); ?>
                    </p>
                </div>
            </div>
            <div class="connection-indicator" aria-hidden="true">
                <span class="status-dot connected" title="<?php esc_attr_e('Connected', 'openai-chatbot'); ?>"></span>
            </div>
        </div>
        
        <!-- Messages Area -->
        <div class="chatbot-inline-messages">
            <div class="messages-container" role="log" aria-live="polite" aria-label="<?php esc_attr_e('Chat messages', 'openai-chatbot'); ?>">
                <!-- Welcome Message -->
                <div class="welcome-message">
                    <div class="message bot-message" role="article">
                        <div class="message-avatar" aria-hidden="true">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        <div class="message-bubble">
                            <div class="message-content">
                                <p><?php echo wp_kses_post($welcome_message); ?></p>
                            </div>
                            <?php if ($show_timestamps): ?>
                                <div class="message-time" aria-label="<?php esc_attr_e('Message time', 'openai-chatbot'); ?>">
                                    <?php echo esc_html(current_time('H:i')); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Quick Questions -->
                <?php if (!empty($quick_questions)): ?>
                    <?php
                    $questions_array = array_filter(array_map('trim', explode("\n", $quick_questions)));
                    if (!empty($questions_array)):
                    ?>
                        <div class="quick-questions">
                            <div class="quick-questions-container">
                                <?php foreach ($questions_array as $index => $question): ?>
                                    <button type="button" class="quick-question-btn" data-question="<?php echo esc_attr($question); ?>">
                                        <?php echo esc_html($question); ?>
                                    </button>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
            
            <!-- Typing Indicator -->
            <div class="typing-indicator" style="display: none;" aria-hidden="true">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <span class="typing-text"><?php esc_html_e('AI is thinking...', 'openai-chatbot'); ?></span>
            </div>
        </div>
        
        <!-- Input Area -->
        <div class="chatbot-inline-input">
            <form class="input-container" role="form" aria-label="<?php esc_attr_e('Send message', 'openai-chatbot'); ?>">
                <label for="<?php echo esc_attr($instance_id); ?>-input" class="sr-only">
                    <?php esc_html_e('Type your message', 'openai-chatbot'); ?>
                </label>
                <textarea 
                    id="<?php echo esc_attr($instance_id); ?>-input"
                    class="message-input" 
                    placeholder="<?php echo esc_attr($placeholder); ?>"
                    maxlength="<?php echo esc_attr($max_length); ?>"
                    rows="1"
                    aria-describedby="<?php echo esc_attr($instance_id); ?>-character-count"
                    autocomplete="off"
                    spellcheck="true"
                ></textarea>
                
                <button 
                    class="send-button" 
                    type="submit" 
                    disabled 
                    aria-label="<?php esc_attr_e('Send message', 'openai-chatbot'); ?>"
                >
                    <!-- Send Icon -->
                    <svg class="send-icon" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                    
                    <!-- Loading Icon -->
                    <svg class="loading-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;" aria-hidden="true">
                        <path d="M12 4V2A10 10 0 0 0 2 12h2a8 8 0 0 1 8-8z"/>
                    </svg>
                </button>
            </form>
            
            <!-- Input Footer -->
            <div class="input-footer">
                <div id="<?php echo esc_attr($instance_id); ?>-character-count" class="character-count" aria-live="polite">
                    <span class="current">0</span>/<span class="max"><?php echo esc_html($max_length); ?></span>
                </div>
                <div class="powered-by">
                    <?php 
                    $powered_by_text = get_option('openai_chatbot_powered_by_text', __('Powered by OpenAI', 'openai-chatbot'));
                    echo esc_html($powered_by_text);
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Inline chatbot specific styles */
.openai-chatbot-shortcode {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    border: 1px solid #ddd;
    border-radius: 12px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin: 20px 0;
}

.chatbot-inline-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chatbot-inline-header {
    background: #007cba;
    color: white;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
}

.chatbot-inline-header .header-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.chatbot-inline-header .bot-avatar {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.chatbot-inline-header .bot-avatar svg {
    width: 18px;
    height: 18px;
}

.chatbot-inline-header .bot-info h3 {
    font-size: 14px;
    font-weight: 600;
    margin: 0;
}

.chatbot-inline-header .bot-info p {
    font-size: 11px;
    opacity: 0.8;
    margin: 0;
}

.chatbot-inline-header .connection-indicator {
    width: 10px;
    height: 10px;
}

.chatbot-inline-header .status-dot {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: #4caf50;
}

.chatbot-inline-messages {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #f0f0f1;
}

.chatbot-inline-messages .messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    scroll-behavior: smooth;
}

.chatbot-inline-messages .messages-container::-webkit-scrollbar {
    width: 6px;
}

.chatbot-inline-messages .messages-container::-webkit-scrollbar-track {
    background: transparent;
}

.chatbot-inline-messages .messages-container::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.chatbot-inline-input {
    background: white;
    border-top: 1px solid #ddd;
    flex-shrink: 0;
}

.chatbot-inline-input .input-container {
    display: flex;
    align-items: flex-end;
    gap: 8px;
    padding: 12px;
}

.chatbot-inline-input .message-input {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 10px 12px;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.4;
    resize: none;
    outline: none;
    transition: border-color 0.3s ease;
    min-height: 38px;
    max-height: 100px;
}

.chatbot-inline-input .message-input:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.1);
}

.chatbot-inline-input .send-button {
    width: 38px;
    height: 38px;
    background: #007cba;
    border: none;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.chatbot-inline-input .send-button:hover:not(:disabled) {
    background: #005a87;
}

.chatbot-inline-input .send-button:disabled {
    background: #757575;
    cursor: not-allowed;
}

.chatbot-inline-input .send-button svg {
    width: 18px;
    height: 18px;
}

.chatbot-inline-input .input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 12px 10px;
    font-size: 11px;
    color: #757575;
}

.chatbot-inline-input .character-count.warning {
    color: #f44336;
    font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 600px) {
    .openai-chatbot-shortcode {
        margin: 10px 0;
    }
    
    .chatbot-inline-header {
        padding: 10px 12px;
    }
    
    .chatbot-inline-messages .messages-container {
        padding: 10px;
    }
    
    .chatbot-inline-input .input-container {
        padding: 10px;
    }
}

/* Quick Questions Styles for Shortcode */
.openai-chatbot-shortcode .quick-questions {
    margin: 16px 0;
    padding: 0 12px;
}

.openai-chatbot-shortcode .quick-questions-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.openai-chatbot-shortcode .quick-question-btn {
    background: linear-gradient(135deg, #1ca08a 0%, #16a085 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    font-family: inherit;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    line-height: 1.4;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.openai-chatbot-shortcode .quick-question-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.3s ease;
}

.openai-chatbot-shortcode .quick-question-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    filter: brightness(1.1);
}

.openai-chatbot-shortcode .quick-question-btn:hover::before {
    left: 100%;
}

.openai-chatbot-shortcode .quick-question-btn:active {
    transform: translateY(0);
}

.openai-chatbot-shortcode .quick-question-btn:focus {
    outline: 2px solid #1ca08a;
    outline-offset: 2px;
}

/* Error message styles */
.openai-chatbot-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 12px;
    border-radius: 4px;
    margin: 10px 0;
    font-size: 14px;
}
</style>

<script>
// Initialize inline chatbot instance
jQuery(document).ready(function($) {
    if (typeof OpenAIChatbot !== 'undefined') {
        new OpenAIChatbot({
            container: '#<?php echo esc_js($instance_id); ?>',
            inline: true,
            autoOpen: true
        });
    }
});
</script>