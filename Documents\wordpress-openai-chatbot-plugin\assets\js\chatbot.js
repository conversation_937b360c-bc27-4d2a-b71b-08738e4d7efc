/**
 * OpenAI Chatbot Frontend JavaScript
 *
 * Handles chat interface interactions, API communication, and user experience
 * Implements real-time messaging, error handling, and conversation persistence
 *
 * @package OpenAI_Chatbot
 */

(function($) {
    'use strict';

    // Debug: Verify this updated JavaScript file is loaded
    console.log('🚀 OpenAI Chatbot JavaScript UPDATED VERSION loaded at:', new Date().toISOString());

    /**
     * OpenAI Chatbot Class
     * 
     * Main class that handles all frontend chatbot functionality
     */
    class OpenAIChatbot {
        
        /**
         * Constructor
         * 
         * @param {Object} options Configuration options
         */
        constructor(options = {}) {
            // Check if required global variables are available
            if (typeof openai_chatbot_ajax === 'undefined') {
                console.error('OpenAI Chatbot: Required JavaScript variables not loaded');
                return;
            }

            this.options = {
                container: '#openai-chatbot',
                apiUrl: (openai_chatbot_ajax.rest_url || '/wp-json/openai-chatbot/v1/') + 'chat',
                nonce: openai_chatbot_ajax.nonce || '',
                maxMessageLength: (openai_chatbot_ajax.settings && openai_chatbot_ajax.settings.max_message_length) || 1000,
                typingDelay: (openai_chatbot_ajax.settings && openai_chatbot_ajax.settings.typing_delay) || 50,
                autoScroll: (openai_chatbot_ajax.settings && openai_chatbot_ajax.settings.auto_scroll) !== false,
                showTimestamps: (openai_chatbot_ajax.settings && openai_chatbot_ajax.settings.show_timestamps) || false,
                storageKey: 'openai_chatbot_conversation',
                retryAttempts: 3,
                retryDelay: 1000,
                settings: openai_chatbot_ajax.settings || {},
                ...options
            };

            this.state = {
                isOpen: false,
                isLoading: false,
                conversationId: null,
                messageHistory: [],
                retryCount: 0,
                lastMessageTime: 0,
                typingTimer: null,
                connectionStatus: 'disconnected'
            };

            this.elements = {};
            this.strings = (openai_chatbot_ajax && openai_chatbot_ajax.strings) || {
                placeholder: 'Type your message...',
                bot_name: 'AI Assistant',
                online: 'Online',
                minimize: 'Minimize',
                welcome: 'Hello! How can I help you today?',
                thinking: 'AI is thinking...',
                error: 'Sorry, something went wrong. Please try again.',
                rate_limit: 'Please wait before sending another message.',
                permission_denied: 'Permission denied. Please refresh the page and try again.',
                retrying: 'Retrying...',
                powered_by: 'Powered by OpenAI'
            };
            
            // Debug: Log the received settings to verify they're being passed correctly
            console.log('🔧 Chatbot Settings Debug:', {
                strings: this.strings,
                settings: this.options.settings,
                quick_questions: this.options.settings.quick_questions
            });

            this.init();
        }

        /**
         * Initialize the chatbot
         */
        init() {
            this.createElements();
            this.bindEvents();
            this.loadConversationHistory();
            this.updateConnectionStatus();
            this.applyColorSettings();

            // Auto-open if specified
            if (this.options.autoOpen) {
                this.openChat();
            }

            console.log('OpenAI Chatbot initialized');
        }

        /**
         * Apply color settings from admin dashboard
         */
        applyColorSettings() {
            if (!this.options.settings || !this.options.settings.colors) {
                console.log('No color settings found, using defaults');
                return;
            }

            const colors = this.options.settings.colors;
            const $widget = $(this.options.container);

            try {
                // Apply primary color and related variables
                if (colors.primary) {
                    $widget.css('--chatbot-primary-color', colors.primary);
                    $widget.css('--chatbot-user-bg-color', colors.primary);
                    $widget.css('--chatbot-border-focus', colors.primary);

                    // Generate hover and dark variants
                    const primaryHover = this.adjustColorBrightness(colors.primary, -10);
                    const primaryDark = this.adjustColorBrightness(colors.primary, -20);
                    $widget.css('--chatbot-primary-hover', primaryHover);
                    $widget.css('--chatbot-primary-dark', primaryDark);

                    // Update focus shadow with primary color
                    const primaryRgb = this.hexToRgb(colors.primary);
                    if (primaryRgb) {
                        $widget.css('--chatbot-shadow-focus', `0 0 0 3px rgba(${primaryRgb.r}, ${primaryRgb.g}, ${primaryRgb.b}, 0.1)`);
                        $widget.css('--chatbot-focus-outline', `2px solid ${colors.primary}`);
                    }
                }

                // Apply secondary color
                if (colors.secondary) {
                    $widget.css('--chatbot-secondary-color', colors.secondary);
                }

                // Apply accent color and related variables
                if (colors.accent) {
                    $widget.css('--chatbot-accent-color', colors.accent);

                    // Generate accent hover variant
                    const accentHover = this.adjustColorBrightness(colors.accent, -10);
                    $widget.css('--chatbot-accent-hover', accentHover);
                }

                // Apply text colors
                if (colors.bot_text) {
                    $widget.css('--chatbot-bot-text-color', colors.bot_text);
                }
                if (colors.user_text) {
                    $widget.css('--chatbot-user-text-color', colors.user_text);
                }

                // Update gradients with new colors
                this.updateGradients($widget, colors);

                // Apply theme-specific adjustments
                this.applyThemeAdjustments($widget, colors);

                console.log('Enhanced color settings applied:', colors);

                // Trigger custom event for other components
                $widget.trigger('chatbot:colorsApplied', [colors]);

            } catch (error) {
                console.error('Error applying color settings:', error);
                // Fallback to default colors if there's an error
                this.applyDefaultColors($widget);
            }
        }

        /**
         * Update gradient variables based on current colors
         */
        updateGradients($widget, colors) {
            if (colors.primary && colors.secondary) {
                $widget.css('--chatbot-gradient-primary',
                    `linear-gradient(135deg, ${colors.primary} 0%, ${colors.secondary} 100%)`);

                const primaryHover = this.adjustColorBrightness(colors.primary, -10);
                const secondaryHover = this.adjustColorBrightness(colors.secondary, -10);
                $widget.css('--chatbot-gradient-primary-hover',
                    `linear-gradient(135deg, ${primaryHover} 0%, ${secondaryHover} 100%)`);
            }

            if (colors.accent) {
                const accentHover = this.adjustColorBrightness(colors.accent, -10);
                $widget.css('--chatbot-gradient-accent',
                    `linear-gradient(135deg, ${colors.accent} 0%, ${accentHover} 100%)`);
            }
        }

        /**
         * Apply theme-specific color adjustments
         */
        applyThemeAdjustments($widget, colors) {
            // Determine if we're using a dark theme based on primary color
            const isDarkTheme = this.isColorDark(colors.primary);

            if (isDarkTheme) {
                // Adjust text colors for dark themes
                $widget.css('--chatbot-text-color', '#f9fafb');
                $widget.css('--chatbot-text-light', '#d1d5db');
                $widget.css('--chatbot-background', '#1f2937');
                $widget.css('--chatbot-surface', '#374151');
            } else {
                // Reset to light theme defaults
                $widget.css('--chatbot-text-color', '#1f2937');
                $widget.css('--chatbot-text-light', '#6b7280');
                $widget.css('--chatbot-background', '#ffffff');
                $widget.css('--chatbot-surface', '#f8fafc');
            }
        }

        /**
         * Apply default colors as fallback
         */
        applyDefaultColors($widget) {
            const defaultColors = {
                primary: '#e74266',
                secondary: '#cf3c5c',
                accent: '#1ca08a',
                bot_text: '#1f2937',
                user_text: '#ffffff'
            };

            this.updateGradients($widget, defaultColors);
            console.log('Applied default colors as fallback');
        }

        /**
         * Utility: Convert hex color to RGB
         */
        hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        }

        /**
         * Utility: Adjust color brightness
         */
        adjustColorBrightness(hex, percent) {
            const rgb = this.hexToRgb(hex);
            if (!rgb) return hex;

            const adjust = (color) => {
                const adjusted = Math.round(color * (100 + percent) / 100);
                return Math.max(0, Math.min(255, adjusted));
            };

            const r = adjust(rgb.r).toString(16).padStart(2, '0');
            const g = adjust(rgb.g).toString(16).padStart(2, '0');
            const b = adjust(rgb.b).toString(16).padStart(2, '0');

            return `#${r}${g}${b}`;
        }

        /**
         * Utility: Determine if a color is dark
         */
        isColorDark(hex) {
            const rgb = this.hexToRgb(hex);
            if (!rgb) return false;

            // Calculate luminance
            const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;
            return luminance < 0.5;
        }

        /**
         * Create DOM elements for the chatbot
         */
        createElements() {
            const $container = $(this.options.container);
            
            if ($container.length === 0) {
                console.error('Chatbot container not found:', this.options.container);
                return;
            }

            // Create chatbot HTML structure
            const chatbotHTML = `
                <div class="openai-chatbot-widget" data-status="closed">
                    <div class="chatbot-toggle">
                        <div class="toggle-icon">
                            <svg class="chat-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                            </svg>
                            <svg class="close-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                            </svg>
                        </div>
                        <div class="connection-indicator">
                            <span class="status-dot"></span>
                        </div>
                    </div>
                    
                    <div class="chatbot-window">
                        <div class="chatbot-header">
                            <div class="header-content">
                                <div class="bot-avatar">
                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                    </svg>
                                </div>
                                <div class="bot-info">
                                    <h3 class="bot-name">${this.strings.bot_name || 'AI Assistant'}</h3>
                                    <p class="bot-status">${this.strings.online || 'Online'}</p>
                                </div>
                            </div>
                            <button class="minimize-btn" type="button" aria-label="${this.strings.minimize || 'Minimize'}">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19 13H5v-2h14v2z"/>
                                </svg>
                            </button>
                        </div>
                        
                        <div class="chatbot-messages">
                            <div class="messages-container">
                                <div class="welcome-message">
                                    <div class="message bot-message">
                                        <div class="message-content">
                                            <p>${this.strings.welcome || 'Hello! How can I help you today?'}</p>
                                        </div>
                                        ${this.options.showTimestamps ? '<div class="message-time">' + this.formatTime(new Date()) + '</div>' : ''}
                                    </div>
                                </div>
                                ${this.generateQuickQuestionsHTML()}
                            </div>
                            <div class="typing-indicator" style="display: none;">
                                <div class="typing-dots">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </div>
                                <span class="typing-text">${this.strings.thinking || 'AI is thinking...'}</span>
                            </div>
                        </div>
                        
                        <div class="chatbot-input">
                            <div class="input-container">
                                <textarea 
                                    class="message-input" 
                                    placeholder="${this.strings.placeholder || 'Type your message...'}"
                                    maxlength="${this.options.maxMessageLength}"
                                    rows="1"
                                ></textarea>
                                <button class="send-button" type="button" disabled>
                                    <svg class="send-icon" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                                    </svg>
                                    <svg class="loading-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                                        <path d="M12 4V2A10 10 0 0 0 2 12h2a8 8 0 0 1 8-8z"/>
                                    </svg>
                                </button>
                            </div>
                            <div class="input-footer">
                                <div class="character-count">
                                    <span class="current">0</span>/<span class="max">${this.options.maxMessageLength}</span>
                                </div>
                                <div class="powered-by">
                                    ${this.strings.powered_by || 'Powered by OpenAI'}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $container.html(chatbotHTML);

            // Cache DOM elements
            this.elements = {
                widget: $container.find('.openai-chatbot-widget'),
                toggle: $container.find('.chatbot-toggle'),
                window: $container.find('.chatbot-window'),
                header: $container.find('.chatbot-header'),
                minimizeBtn: $container.find('.minimize-btn'),
                messagesContainer: $container.find('.messages-container'),
                typingIndicator: $container.find('.typing-indicator'),
                messageInput: $container.find('.message-input'),
                sendButton: $container.find('.send-button'),
                characterCount: $container.find('.character-count .current'),
                connectionIndicator: $container.find('.connection-indicator .status-dot')
            };
        }

        /**
         * Bind event handlers
         */
        bindEvents() {
            // Toggle chat window
            this.elements.toggle.on('click', () => {
                this.toggleChat();
            });

            // Minimize chat
            this.elements.minimizeBtn.on('click', () => {
                this.closeChat();
            });

            // Send message on button click
            this.elements.sendButton.on('click', () => {
                this.sendMessage();
            });

            // Handle quick action buttons
            $(document).on('click', '.quick-action-btn', (e) => {
                const message = $(e.target).data('message');
                if (message) {
                    this.elements.messageInput.val(message);
                    this.sendMessage();
                    // Hide quick actions after first use
                    $('.quick-actions').fadeOut(300);
                }
            });

            // Handle quick question buttons
            $(document).on('click', '.quick-question-btn', (e) => {
                const question = $(e.target).data('question');
                if (question) {
                    this.elements.messageInput.val(question);
                    this.sendMessage();
                    // Hide quick questions after first use
                    $('.quick-questions').fadeOut(300);
                }
            });

            // Send message on Enter key (Shift+Enter for new line)
            this.elements.messageInput.on('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                } else if (e.key === 'Enter' && e.shiftKey) {
                    // Allow new line
                    this.autoResizeTextarea();
                }
            });

            // Auto-resize textarea and update character count
            this.elements.messageInput.on('input', () => {
                this.autoResizeTextarea();
                this.updateCharacterCount();
                this.updateSendButton();
            });

            // Handle paste events
            this.elements.messageInput.on('paste', (e) => {
                setTimeout(() => {
                    this.autoResizeTextarea();
                    this.updateCharacterCount();
                    this.updateSendButton();
                }, 10);
            });

            // Close chat when clicking outside (optional)
            $(document).on('click', (e) => {
                if (this.state.isOpen && 
                    !this.elements.widget.is(e.target) && 
                    this.elements.widget.has(e.target).length === 0) {
                    // Uncomment to enable click-outside-to-close
                    // this.closeChat();
                }
            });

            // Handle window resize
            $(window).on('resize', () => {
                this.handleResize();
            });

            // Handle visibility change (tab switching)
            $(document).on('visibilitychange', () => {
                if (!document.hidden && this.state.isOpen) {
                    this.scrollToBottom();
                }
            });
        }

        /**
         * Toggle chat window open/closed
         */
        toggleChat() {
            if (this.state.isOpen) {
                this.closeChat();
            } else {
                this.openChat();
            }
        }

        /**
         * Open chat window
         */
        openChat() {
            this.state.isOpen = true;
            this.elements.widget.attr('data-status', 'open');
            this.elements.messageInput.focus();
            this.scrollToBottom();
            
            // Trigger custom event
            $(document).trigger('openai_chatbot_opened');
        }

        /**
         * Close chat window
         */
        closeChat() {
            this.state.isOpen = false;
            this.elements.widget.attr('data-status', 'closed');
            
            // Trigger custom event
            $(document).trigger('openai_chatbot_closed');
        }

        /**
         * Send message to the API
         */
        async sendMessage() {
            const message = this.elements.messageInput.val().trim();
            
            if (!message || this.state.isLoading) {
                return;
            }

            // Validate message length
            if (message.length > this.options.maxMessageLength) {
                this.showError(this.strings.message_too_long || 'Message is too long');
                return;
            }

            // Add user message to chat
            this.addMessage(message, 'user');
            
            // Clear input and update UI
            this.elements.messageInput.val('').trigger('input');
            this.setLoadingState(true);
            this.showTypingIndicator();

            try {
                const response = await this.makeApiRequest(message);

                // Check if response exists and has expected structure
                if (!response) {
                    throw new Error('No response received from server');
                }

                // Log specific properties we're looking for
                console.log('response.success:', response.success);
                console.log('response.message:', response.message);
                console.log('response.data:', response.data);
                console.log('response.conversation_id:', response.conversation_id);
                console.log('=== CHATBOT DEBUG END ===');

                // WordPress REST API might return response directly or wrapped in data
                // Handle both cases: direct response and WordPress REST wrapped response
                let actualResponse = response;

                // If response has a 'data' property and no 'success' property, it might be WordPress REST wrapped
                if (response.data && !response.success && !response.message) {
                    console.log('Detected WordPress REST API wrapped response, using response.data');
                    actualResponse = response.data;
                }

                if (actualResponse && (actualResponse.success || actualResponse.message)) {
                    // Handle different response structures
                    let botMessage = '';
                    let conversationId = null;

                    console.log('Processing message from actualResponse...');
                    console.log('actualResponse.success:', actualResponse.success);
                    console.log('actualResponse.message:', actualResponse.message);

                    if (actualResponse.data && actualResponse.data.message) {
                        console.log('Using actualResponse.data.message structure');
                        // Response structure: { success: true, data: { message: "...", conversation_id: "..." } }
                        botMessage = actualResponse.data.message;
                        conversationId = actualResponse.data.conversation_id;
                    } else if (actualResponse.message) {
                        console.log('Using actualResponse.message structure');
                        // Response structure: { success: true, message: "...", conversation_id: "..." }
                        botMessage = actualResponse.message;
                        conversationId = actualResponse.conversation_id;
                    } else {
                        console.log('Using fallback message structure');
                        // Fallback - try to find message in response
                        botMessage = actualResponse.content || actualResponse.text || this.strings.error || 'No response received';
                        conversationId = actualResponse.conversation_id || actualResponse.id;
                    }

                    console.log('Final botMessage:', botMessage);
                    console.log('Final conversationId:', conversationId);

                    // Validate that we have a message
                    if (!botMessage || botMessage.trim() === '') {
                        console.error('ERROR: No valid message found in response');
                        botMessage = this.strings.error || 'No response received';
                    }

                    // Add bot response
                    this.addMessage(botMessage, 'bot');
                    if (conversationId) {
                        this.state.conversationId = conversationId;
                    }
                    this.state.retryCount = 0;
                } else {
                    console.log('Response failed or invalid:', response);
                    console.log('actualResponse:', actualResponse);
                    console.log('actualResponse.success is:', actualResponse ? actualResponse.success : 'actualResponse is null/undefined');
                    const errorMessage = (actualResponse && (actualResponse.message || actualResponse.error)) || this.strings.error || 'Something went wrong';
                    throw new Error(errorMessage);
                }
                
            } catch (error) {
                console.error('Chat API Error:', error);
                this.handleApiError(error, message);
            } finally {
                this.setLoadingState(false);
                this.hideTypingIndicator();
                this.saveConversationHistory();
            }
        }

        /**
         * Make API request to send message
         * 
         * @param {string} message User message
         * @returns {Promise} API response
         */
        async makeApiRequest(message) {
            const requestData = {
                message: message,
                conversation_id: this.state.conversationId
            };

            try {
                const response = await fetch(this.options.apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': this.options.nonce
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    if (response.status === 429) {
                        throw new Error(this.strings.rate_limit || 'Rate limit exceeded');
                    } else if (response.status === 403) {
                        throw new Error(this.strings.permission_denied || 'Permission denied');
                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                }

                // Try to parse JSON response
                const responseText = await response.text();
                if (!responseText.trim()) {
                    throw new Error('Empty response from server');
                }

                try {
                    return JSON.parse(responseText);
                } catch (parseError) {
                    console.error('JSON parse error:', parseError);
                    console.error('Response text:', responseText);
                    throw new Error('Invalid response format from server');
                }
            } catch (error) {
                // Re-throw with more context if it's a network error
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    throw new Error('Network error: Unable to connect to server');
                }
                throw error;
            }
        }

        /**
         * Handle API errors with retry logic
         * 
         * @param {Error} error The error object
         * @param {string} originalMessage The original message to retry
         */
        async handleApiError(error, originalMessage) {
            if (this.state.retryCount < this.options.retryAttempts) {
                this.state.retryCount++;
                
                // Show retry message
                this.addMessage(
                    `${this.strings.retrying || 'Retrying...'} (${this.state.retryCount}/${this.options.retryAttempts})`,
                    'system'
                );

                // Wait before retry
                await this.delay(this.options.retryDelay * this.state.retryCount);
                
                // Retry the request
                try {
                    this.setLoadingState(true);
                    this.showTypingIndicator();

                    const response = await this.makeApiRequest(originalMessage);

                    // Use the same response handling logic as in sendMessage
                    let actualResponse = response;

                    // If response has a 'data' property and no 'success' property, it might be WordPress REST wrapped
                    if (response && response.data && !response.success && !response.message) {
                        console.log('Retry: Detected WordPress REST API wrapped response, using response.data');
                        actualResponse = response.data;
                    }

                    if (actualResponse && (actualResponse.success || actualResponse.message)) {
                        // Remove retry message
                        this.removeLastSystemMessage();

                        // Handle different response structures (same as in sendMessage)
                        let botMessage = '';
                        let conversationId = null;

                        if (actualResponse.data && actualResponse.data.message) {
                            botMessage = actualResponse.data.message;
                            conversationId = actualResponse.data.conversation_id;
                        } else if (actualResponse.message) {
                            botMessage = actualResponse.message;
                            conversationId = actualResponse.conversation_id;
                        } else {
                            botMessage = actualResponse.content || actualResponse.text || this.strings.error || 'No response received';
                            conversationId = actualResponse.conversation_id || actualResponse.id;
                        }

                        // Add successful response
                        this.addMessage(botMessage, 'bot');
                        if (conversationId) {
                            this.state.conversationId = conversationId;
                        }
                        this.state.retryCount = 0;
                    } else {
                        const errorMessage = (actualResponse && (actualResponse.message || actualResponse.error || actualResponse.data)) || this.strings.error || 'Retry failed';
                        throw new Error(errorMessage);
                    }
                } catch (retryError) {
                    console.error('Retry failed:', retryError);
                    this.handleApiError(retryError, originalMessage);
                }
            } else {
                // Max retries reached
                this.addMessage(
                    error.message || this.strings.error || 'Sorry, something went wrong. Please try again.',
                    'error'
                );
                this.state.retryCount = 0;
            }
        }

        /**
         * Add message to chat
         * 
         * @param {string} content Message content
         * @param {string} type Message type (user, bot, system, error)
         */
        addMessage(content, type = 'user') {
            // Validate content
            if (content === null || content === undefined) {
                content = 'Error: No message content';
            }

            // Ensure content is a string
            if (typeof content !== 'string') {
                console.warn('WARNING: addMessage content is not a string, converting...');
                content = String(content);
            }

            const timestamp = new Date();
            const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            const messageData = {
                id: messageId,
                content: content,
                type: type,
                timestamp: timestamp
            };

            console.log('messageData created:', messageData);

            // Add to message history
            this.state.messageHistory.push(messageData);

            // Create message HTML
            try {
                const messageHTML = this.createMessageHTML(messageData);

                // Add to DOM with animation
                const $message = $(messageHTML);
                this.elements.messagesContainer.append($message);

                // Animate message appearance
                setTimeout(() => {
                    $message.addClass('message-visible');
                }, 10);

                // Auto-scroll to bottom
                if (this.options.autoScroll) {
                    this.scrollToBottom();
                }

                // Trigger custom event
                $(document).trigger('openai_chatbot_message_added', [messageData]);

            } catch (error) {
                console.error('Error adding message:', error);
                throw error;
            }
        }

        /**
         * Create HTML for a message
         * 
         * @param {Object} messageData Message data object
         * @returns {string} Message HTML
         */
        createMessageHTML(messageData) {
            console.log('=== CREATE MESSAGE HTML DEBUG ===');
            console.log('messageData:', messageData);

            if (!messageData) {
                console.error('ERROR: messageData is null/undefined');
                throw new Error('messageData is required');
            }

            const { content, type, timestamp } = messageData;
            console.log('Destructured - content:', content, 'type:', type, 'timestamp:', timestamp);

            if (!content) {
                console.error('ERROR: content is null/undefined in messageData');
                throw new Error('Message content is required');
            }

            const timeString = this.formatTime(timestamp);
            const escapedContent = this.escapeHtml(content);

            let messageClass = `message ${type}-message`;
            let avatarHTML = '';

            console.log('Processing message - class:', messageClass, 'escapedContent:', escapedContent);
            
            if (type === 'bot') {
                avatarHTML = `
                    <div class="message-avatar">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                `;
            } else if (type === 'user') {
                avatarHTML = `
                    <div class="message-avatar user-avatar">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                    </div>
                `;
            }

            return `
                <div class="${messageClass}" data-message-id="${messageData.id}">
                    ${avatarHTML}
                    <div class="message-bubble">
                        <div class="message-content">
                            ${this.formatMessageContent(escapedContent)}
                        </div>
                        ${this.options.showTimestamps ? `<div class="message-time">${timeString}</div>` : ''}
                    </div>
                </div>
            `;
        }

        /**
         * Format message content (handle line breaks, links, etc.)
         * 
         * @param {string} content Raw message content
         * @returns {string} Formatted HTML content
         */
        formatMessageContent(content) {
            // Convert line breaks to <br>
            content = content.replace(/\n/g, '<br>');
            
            // Convert URLs to links (basic implementation)
            content = content.replace(
                /(https?:\/\/[^\s<>"]+)/gi,
                '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>'
            );
            
            return content;
        }

        /**
         * Escape HTML characters
         * 
         * @param {string} text Text to escape
         * @returns {string} Escaped text
         */
        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        /**
         * Format timestamp for display
         * 
         * @param {Date} date Date object
         * @returns {string} Formatted time string
         */
        formatTime(date) {
            return date.toLocaleTimeString([], { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
        }

        /**
         * Generate HTML for quick questions based on settings
         *
         * @returns {string} HTML string for quick questions
         */
        generateQuickQuestionsHTML() {
            // Get quick questions from settings
            const quickQuestions = (this.options.settings && this.options.settings.quick_questions) ||
                                 (openai_chatbot_ajax && openai_chatbot_ajax.settings && openai_chatbot_ajax.settings.quick_questions) ||
                                 [];
            
            if (!quickQuestions || quickQuestions.length === 0) {
                return '';
            }
            
            let html = '<div class="quick-questions"><div class="quick-questions-container">';
            
            quickQuestions.forEach((question, index) => {
                if (question && question.trim()) {
                    html += `<button type="button" class="quick-question-btn" data-question="${this.escapeHtml(question.trim())}">${this.escapeHtml(question.trim())}</button>`;
                }
            });
            
            html += '</div></div>';
            
            return html;
        }

        /**
         * Show typing indicator
         */
        showTypingIndicator() {
            this.elements.typingIndicator.show();
            this.scrollToBottom();
        }

        /**
         * Hide typing indicator
         */
        hideTypingIndicator() {
            this.elements.typingIndicator.hide();
        }

        /**
         * Set loading state
         * 
         * @param {boolean} loading Loading state
         */
        setLoadingState(loading) {
            this.state.isLoading = loading;
            
            if (loading) {
                this.elements.sendButton.prop('disabled', true);
                this.elements.sendButton.find('.send-icon').hide();
                this.elements.sendButton.find('.loading-icon').show().addClass('spinning');
                this.elements.messageInput.prop('disabled', true);
            } else {
                this.elements.sendButton.prop('disabled', false);
                this.elements.sendButton.find('.send-icon').show();
                this.elements.sendButton.find('.loading-icon').hide().removeClass('spinning');
                this.elements.messageInput.prop('disabled', false);
                this.updateSendButton();
            }
        }

        /**
         * Update send button state based on input
         */
        updateSendButton() {
            const hasText = this.elements.messageInput.val().trim().length > 0;
            this.elements.sendButton.prop('disabled', !hasText || this.state.isLoading);
        }

        /**
         * Update character count display
         */
        updateCharacterCount() {
            const currentLength = this.elements.messageInput.val().length;
            this.elements.characterCount.text(currentLength);
            
            // Add warning class if approaching limit
            const warningThreshold = this.options.maxMessageLength * 0.9;
            this.elements.characterCount.parent().toggleClass('warning', currentLength > warningThreshold);
        }

        /**
         * Auto-resize textarea based on content
         */
        autoResizeTextarea() {
            const textarea = this.elements.messageInput[0];
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        /**
         * Scroll messages to bottom
         */
        scrollToBottom() {
            const container = this.elements.messagesContainer[0];
            container.scrollTop = container.scrollHeight;
        }

        /**
         * Remove last system message (used for retry cleanup)
         */
        removeLastSystemMessage() {
            const lastMessage = this.elements.messagesContainer.find('.system-message').last();
            if (lastMessage.length) {
                lastMessage.remove();
                // Also remove from history
                for (let i = this.state.messageHistory.length - 1; i >= 0; i--) {
                    if (this.state.messageHistory[i].type === 'system') {
                        this.state.messageHistory.splice(i, 1);
                        break;
                    }
                }
            }
        }

        /**
         * Show error message
         * 
         * @param {string} message Error message
         */
        showError(message) {
            this.addMessage(message, 'error');
        }

        /**
         * Update connection status indicator
         */
        updateConnectionStatus() {
            // Simple connectivity check
            if (navigator.onLine) {
                this.state.connectionStatus = 'connected';
                this.elements.connectionIndicator.removeClass('disconnected').addClass('connected');
            } else {
                this.state.connectionStatus = 'disconnected';
                this.elements.connectionIndicator.removeClass('connected').addClass('disconnected');
            }
        }

        /**
         * Handle window resize
         */
        handleResize() {
            if (this.state.isOpen) {
                this.scrollToBottom();
            }
        }

        /**
         * Save conversation history to localStorage
         */
        saveConversationHistory() {
            try {
                const historyData = {
                    conversationId: this.state.conversationId,
                    messages: this.state.messageHistory.slice(-50), // Keep last 50 messages
                    timestamp: Date.now()
                };
                
                localStorage.setItem(this.options.storageKey, JSON.stringify(historyData));
            } catch (error) {
                console.warn('Failed to save conversation history:', error);
            }
        }

        /**
         * Load conversation history from localStorage
         */
        loadConversationHistory() {
            try {
                const savedData = localStorage.getItem(this.options.storageKey);
                if (!savedData) return;

                const historyData = JSON.parse(savedData);
                
                // Check if data is not too old (24 hours)
                const maxAge = 24 * 60 * 60 * 1000;
                if (Date.now() - historyData.timestamp > maxAge) {
                    localStorage.removeItem(this.options.storageKey);
                    return;
                }

                // Restore conversation
                this.state.conversationId = historyData.conversationId;
                this.state.messageHistory = historyData.messages || [];

                // Render saved messages (skip welcome message)
                this.elements.messagesContainer.find('.welcome-message').hide();
                
                historyData.messages.forEach(messageData => {
                    if (messageData.type !== 'system' && messageData.type !== 'error') {
                        const messageHTML = this.createMessageHTML(messageData);
                        this.elements.messagesContainer.append(messageHTML);
                    }
                });

                if (this.state.messageHistory.length > 0) {
                    this.scrollToBottom();
                }

            } catch (error) {
                console.warn('Failed to load conversation history:', error);
                localStorage.removeItem(this.options.storageKey);
            }
        }

        /**
         * Clear conversation history
         */
        clearHistory() {
            this.state.conversationId = null;
            this.state.messageHistory = [];
            this.elements.messagesContainer.empty();
            this.elements.messagesContainer.append(`
                <div class="welcome-message">
                    <div class="message bot-message">
                        <div class="message-content">
                            <p>${this.strings.welcome || 'Hello! How can I help you today?'}</p>
                        </div>
                        ${this.options.showTimestamps ? '<div class="message-time">' + this.formatTime(new Date()) + '</div>' : ''}
                    </div>
                </div>
            `);
            
            localStorage.removeItem(this.options.storageKey);
        }

        /**
         * Utility function to create delay
         * 
         * @param {number} ms Milliseconds to delay
         * @returns {Promise} Promise that resolves after delay
         */
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        /**
         * Destroy the chatbot instance
         */
        destroy() {
            // Remove event listeners
            this.elements.toggle.off();
            this.elements.minimizeBtn.off();
            this.elements.sendButton.off();
            this.elements.messageInput.off();
            $(document).off('click visibilitychange');
            $(window).off('resize');

            // Clear timers
            if (this.state.typingTimer) {
                clearTimeout(this.state.typingTimer);
            }

            // Remove DOM elements
            this.elements.widget.remove();
            
            console.log('OpenAI Chatbot destroyed');
        }
    }

    /**
     * Initialize chatbot when DOM is ready
     */
    $(document).ready(function() {
        // Check if chatbot container exists
        if ($('#openai-chatbot').length > 0) {
            // Initialize chatbot
            window.openaiChatbot = new OpenAIChatbot();
            
            // Make it globally accessible for debugging
            if (typeof window.console !== 'undefined') {
                console.log('OpenAI Chatbot available as window.openaiChatbot');
            }
        }
    });

    // Handle online/offline events
    $(window).on('online offline', function() {
        if (window.openaiChatbot) {
            window.openaiChatbot.updateConnectionStatus();
        }
    });

})(jQuery);