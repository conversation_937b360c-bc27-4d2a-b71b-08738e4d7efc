<?php
/**
 * Synchronization Test Script
 * 
 * This script tests the complete data flow from admin settings to frontend display
 * Run this after making changes to verify synchronization is working
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // If not in WordPress context, try to load WordPress
    $wp_load_paths = [
        '../../../wp-load.php',
        '../../../../wp-load.php',
        '../../../../../wp-load.php'
    ];
    
    $wp_loaded = false;
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            $wp_loaded = true;
            break;
        }
    }
    
    if (!$wp_loaded) {
        die('WordPress not found. Please run this script from WordPress admin or place it in the correct directory.');
    }
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>OpenAI Chatbot Synchronization Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        .test-section h3 { margin-top: 0; color: #0073aa; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; }
        .button { background: #0073aa; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .button:hover { background: #005a87; }
        .header { background: #0073aa; color: white; padding: 15px; margin: -20px -20px 20px -20px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔄 OpenAI Chatbot Synchronization Test</h1>
        <p>Testing data flow from admin settings to frontend display</p>
    </div>

    <?php
    // Test 1: Check if plugin is active and loaded
    echo '<div class="test-section">';
    echo '<h3>Test 1: Plugin Status</h3>';
    
    if (class_exists('OpenAI_Chatbot_Plugin')) {
        echo '<div class="success">✅ Plugin class loaded successfully</div>';
        $plugin_instance = OpenAI_Chatbot_Plugin::get_instance();
        if ($plugin_instance) {
            echo '<div class="success">✅ Plugin instance created successfully</div>';
        } else {
            echo '<div class="error">❌ Failed to get plugin instance</div>';
        }
    } else {
        echo '<div class="error">❌ Plugin class not found</div>';
    }
    echo '</div>';

    // Test 2: Database Settings Retrieval
    echo '<div class="test-section">';
    echo '<h3>Test 2: Database Settings Retrieval</h3>';
    
    $test_settings = [
        'openai_chatbot_bot_name' => 'Bot Name',
        'openai_chatbot_welcome_message' => 'Welcome Message',
        'openai_chatbot_placeholder' => 'Placeholder Text',
        'openai_chatbot_powered_by_text' => 'Powered By Text',
        'openai_chatbot_quick_questions' => 'Quick Questions'
    ];
    
    $all_settings_ok = true;
    foreach ($test_settings as $setting_key => $setting_name) {
        $value = get_option($setting_key);
        if ($value !== false && !empty($value)) {
            echo "<div class='success'>✅ {$setting_name}: " . esc_html(substr($value, 0, 50)) . (strlen($value) > 50 ? '...' : '') . "</div>";
        } else {
            echo "<div class='warning'>⚠️ {$setting_name}: Empty or not set</div>";
            $all_settings_ok = false;
        }
    }
    
    if ($all_settings_ok) {
        echo '<div class="success">✅ All critical settings are configured</div>';
    } else {
        echo '<div class="warning">⚠️ Some settings may need configuration</div>';
    }
    echo '</div>';

    // Test 3: Cache Clearing Function
    echo '<div class="test-section">';
    echo '<h3>Test 3: Cache Clearing</h3>';
    
    if (class_exists('OpenAI_Chatbot_Plugin')) {
        $plugin_instance = OpenAI_Chatbot_Plugin::get_instance();
        if (method_exists($plugin_instance, 'clear_settings_cache')) {
            $plugin_instance->clear_settings_cache();
            echo '<div class="success">✅ Settings cache cleared successfully</div>';
        } else {
            echo '<div class="error">❌ clear_settings_cache method not found</div>';
        }
    }
    
    // Test cache buster
    $cache_buster = get_option('openai_chatbot_cache_buster');
    if ($cache_buster) {
        echo '<div class="success">✅ Cache buster active: ' . date('Y-m-d H:i:s', $cache_buster) . '</div>';
    } else {
        echo '<div class="warning">⚠️ Cache buster not set</div>';
    }
    echo '</div>';

    // Test 4: JavaScript Localization Data
    echo '<div class="test-section">';
    echo '<h3>Test 4: JavaScript Localization</h3>';
    
    // Simulate the localization process
    $bot_name = get_option('openai_chatbot_bot_name', __('AI Assistant', 'openai-chatbot'));
    $welcome_message = get_option('openai_chatbot_welcome_message', __('Hello! How can I help you today?', 'openai-chatbot'));
    $placeholder = get_option('openai_chatbot_placeholder', __('Type your message...', 'openai-chatbot'));
    $powered_by_text = get_option('openai_chatbot_powered_by_text', __('Powered by OpenAI', 'openai-chatbot'));
    $quick_questions = get_option('openai_chatbot_quick_questions', "What products do you offer?\nHow can I contact support?\nWhat are your business hours?");
    
    $quick_questions_array = array();
    if (!empty($quick_questions)) {
        $quick_questions_array = array_filter(array_map('trim', explode("\n", $quick_questions)));
    }
    
    $localization_data = array(
        'strings' => array(
            'bot_name' => $bot_name,
            'welcome' => $welcome_message,
            'placeholder' => $placeholder,
            'powered_by' => $powered_by_text
        ),
        'settings' => array(
            'quick_questions' => $quick_questions_array,
            'bot_name' => $bot_name,
            'welcome_message' => $welcome_message,
            'powered_by_text' => $powered_by_text
        )
    );
    
    echo '<div class="code">';
    echo '<strong>Localization Data Preview:</strong><br>';
    echo '<pre>' . json_encode($localization_data, JSON_PRETTY_PRINT) . '</pre>';
    echo '</div>';
    
    if (!empty($quick_questions_array)) {
        echo '<div class="success">✅ Quick questions parsed: ' . count($quick_questions_array) . ' questions</div>';
    } else {
        echo '<div class="warning">⚠️ No quick questions found</div>';
    }
    echo '</div>';

    // Test 5: Template Rendering Test
    echo '<div class="test-section">';
    echo '<h3>Test 5: Template Data Flow</h3>';
    
    // Check if template file exists
    $template_path = OPENAI_CHATBOT_PLUGIN_PATH . 'templates/chat-widget.php';
    if (file_exists($template_path)) {
        echo '<div class="success">✅ Template file exists</div>';
        
        // Check template modification time
        $template_mtime = filemtime($template_path);
        echo '<div class="success">✅ Template last modified: ' . date('Y-m-d H:i:s', $template_mtime) . '</div>';
    } else {
        echo '<div class="error">❌ Template file not found</div>';
    }
    echo '</div>';

    // Test 6: Frontend Asset Loading
    echo '<div class="test-section">';
    echo '<h3>Test 6: Asset Loading</h3>';
    
    $js_path = OPENAI_CHATBOT_PLUGIN_PATH . 'assets/js/chatbot.js';
    $css_path = OPENAI_CHATBOT_PLUGIN_PATH . 'assets/css/chatbot.css';
    
    if (file_exists($js_path)) {
        $js_mtime = filemtime($js_path);
        echo '<div class="success">✅ JavaScript file exists (modified: ' . date('Y-m-d H:i:s', $js_mtime) . ')</div>';
    } else {
        echo '<div class="error">❌ JavaScript file not found</div>';
    }
    
    if (file_exists($css_path)) {
        $css_mtime = filemtime($css_path);
        echo '<div class="success">✅ CSS file exists (modified: ' . date('Y-m-d H:i:s', $css_mtime) . ')</div>';
    } else {
        echo '<div class="error">❌ CSS file not found</div>';
    }
    echo '</div>';
    ?>

    <div class="test-section">
        <h3>🧪 Manual Testing Instructions</h3>
        <ol>
            <li><strong>Update Settings:</strong> Go to <a href="<?php echo admin_url('options-general.php?page=openai-chatbot'); ?>" target="_blank">OpenAI Chatbot Settings</a></li>
            <li><strong>Change Values:</strong> Modify the Bot Name, Welcome Message, and Quick Questions</li>
            <li><strong>Save Settings:</strong> Click "Save Changes"</li>
            <li><strong>Clear Browser Cache:</strong> Hard refresh your browser (Ctrl+F5 or Cmd+Shift+R)</li>
            <li><strong>Check Frontend:</strong> Visit your website and open the chatbot</li>
            <li><strong>Verify Changes:</strong> Confirm that the new values appear in the chatbot interface</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>🔧 Troubleshooting Actions</h3>
        <button class="button" onclick="location.reload()">🔄 Refresh This Test</button>
        <button class="button" onclick="window.open('<?php echo admin_url('options-general.php?page=openai-chatbot'); ?>', '_blank')">⚙️ Open Settings</button>
        <button class="button" onclick="window.open('<?php echo home_url(); ?>', '_blank')">🌐 View Frontend</button>
        
        <?php if (current_user_can('manage_options')): ?>
        <form method="post" style="display: inline;">
            <?php wp_nonce_field('force_cache_clear', 'cache_clear_nonce'); ?>
            <button type="submit" name="force_cache_clear" class="button">🧹 Force Cache Clear</button>
        </form>
        <?php endif; ?>
    </div>

    <?php
    // Handle force cache clear
    if (isset($_POST['force_cache_clear']) && wp_verify_nonce($_POST['cache_clear_nonce'], 'force_cache_clear')) {
        // Clear all possible caches
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
        
        if (class_exists('OpenAI_Chatbot_Plugin')) {
            $plugin_instance = OpenAI_Chatbot_Plugin::get_instance();
            if (method_exists($plugin_instance, 'clear_settings_cache')) {
                $plugin_instance->clear_settings_cache();
            }
        }
        
        // Update cache buster
        update_option('openai_chatbot_cache_buster', time());
        
        echo '<div class="test-section success">';
        echo '<h3>✅ Cache Cleared Successfully</h3>';
        echo '<p>All caches have been cleared. Please refresh your frontend to see changes.</p>';
        echo '</div>';
    }
    ?>

    <p style="margin-top: 30px; color: #666; font-size: 12px;">
        Test completed at: <?php echo date('Y-m-d H:i:s'); ?><br>
        WordPress version: <?php echo get_bloginfo('version'); ?><br>
        Plugin version: <?php echo defined('OPENAI_CHATBOT_VERSION') ? OPENAI_CHATBOT_VERSION : 'Unknown'; ?>
    </p>
</body>
</html>