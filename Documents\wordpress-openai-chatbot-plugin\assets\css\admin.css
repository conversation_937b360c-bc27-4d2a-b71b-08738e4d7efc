/**
 * OpenAI Chatbot Admin Styles - Modern Light Theme
 *
 * CSS styles for the WordPress admin interface
 * Provides clean, modern, light-themed styling for the settings page
 *
 * @package OpenAI_Chatbot
 */

/* Admin page layout */
.openai-chatbot-admin {
    display: flex;
    gap: 24px;
    margin-top: 24px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
}

.admin-content {
    flex: 2;
}

.admin-sidebar {
    flex: 1;
    max-width: 320px;
}

/* Modern postbox styling */
.openai-chatbot-admin .postbox {
    margin-bottom: 24px;
    border: 1px solid #e8eaed;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    background: #ffffff;
    overflow: hidden;
    transition: all 0.3s ease;
}

.openai-chatbot-admin .postbox:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.openai-chatbot-admin .postbox-header {
    border-bottom: 1px solid #f0f2f5;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    padding: 0;
}

.openai-chatbot-admin .postbox-header h2 {
    font-size: 16px;
    font-weight: 600;
    padding: 16px 20px;
    margin: 0;
    line-height: 1.4;
    color: #1a1a1a;
    letter-spacing: -0.01em;
}

.openai-chatbot-admin .inside {
    padding: 20px;
    background: #ffffff;
}

/* Modern form styling */
.openai-chatbot-admin .form-table {
    background: transparent;
}

.openai-chatbot-admin .form-table th {
    width: 220px;
    padding: 20px 16px 20px 0;
    vertical-align: top;
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
    letter-spacing: -0.01em;
}

.openai-chatbot-admin .form-table td {
    padding: 20px 16px;
    border-bottom: 1px solid #f5f7fa;
}

.openai-chatbot-admin .form-table tr:last-child td {
    border-bottom: none;
}

.openai-chatbot-admin .form-table input[type="text"],
.openai-chatbot-admin .form-table input[type="number"],
.openai-chatbot-admin .form-table select,
.openai-chatbot-admin .form-table textarea {
    width: 100%;
    max-width: 420px;
    padding: 12px 16px;
    border: 2px solid #e8eaed;
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.4;
    background: #ffffff;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
}

.openai-chatbot-admin .form-table input[type="text"]:focus,
.openai-chatbot-admin .form-table input[type="number"]:focus,
.openai-chatbot-admin .form-table select:focus,
.openai-chatbot-admin .form-table textarea:focus {
    border-color: #4285f4;
    box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
    outline: none;
}

.openai-chatbot-admin .form-table textarea.large-text {
    max-width: 100%;
    min-height: 120px;
    resize: vertical;
}

.openai-chatbot-admin .form-table .small-text {
    width: 100px;
    max-width: 100px;
}

.openai-chatbot-admin .form-table .regular-text {
    width: 320px;
    max-width: 320px;
}

/* Modern checkbox and radio styling */
.openai-chatbot-admin .form-table fieldset {
    margin: 0;
    padding: 0;
    border: none;
}

.openai-chatbot-admin .form-table fieldset label {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-weight: 500;
    color: #374151;
    cursor: pointer;
    transition: color 0.2s ease;
}

.openai-chatbot-admin .form-table fieldset label:hover {
    color: #1f2937;
}

.openai-chatbot-admin .form-table input[type="checkbox"],
.openai-chatbot-admin .form-table input[type="radio"] {
    margin-right: 12px;
    width: 18px;
    height: 18px;
    accent-color: #4285f4;
}

/* Color picker section styling */
.color-picker-section {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 24px;
    border-radius: 12px;
    border: 2px solid #e2e8f0;
    margin-top: 8px;
}

.color-picker-row {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 16px;
}

.color-picker-row label {
    min-width: 200px;
    font-weight: 600;
    color: #2d3748;
    font-size: 14px;
}

.color-preview-section {
    margin-top: 24px;
    padding-top: 20px;
    border-top: 2px solid #e2e8f0;
}

.color-preview-section h4 {
    margin: 0 0 16px 0;
    font-size: 15px;
    font-weight: 600;
    color: #2d3748;
}

.message-preview {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    padding: 20px;
    border-radius: 12px;
    margin: 12px 0 20px 0;
    min-height: 120px;
    border: 1px solid #e2e8f0;
}

.preview-message {
    margin-bottom: 12px;
    display: flex;
    align-items: flex-start;
}

.user-preview {
    justify-content: flex-end;
}

.bot-preview {
    justify-content: flex-start;
}

.preview-content {
    padding: 12px 16px;
    border-radius: 18px;
    max-width: 75%;
    font-size: 14px;
    line-height: 1.5;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.user-content {
    background: #4285f4;
    color: var(--user-text-color, #ffffff);
}

.bot-content {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    color: var(--bot-text-color, #1e1e1e);
}

.reset-colors {
    margin-top: 12px;
    padding: 10px 20px;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    color: #4a5568;
    font-weight: 500;
    transition: all 0.2s ease;
}

.reset-colors:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
    color: #2d3748;
}

/* Modern description text */
.openai-chatbot-admin .description {
    color: #6b7280;
    margin-top: 8px;
    font-size: 13px;
    line-height: 1.5;
    font-weight: 400;
}

/* Modern code blocks */
.openai-chatbot-admin code {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 13px;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    border: 1px solid #e2e8f0;
    color: #4a5568;
}

.openai-chatbot-admin .code {
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 13px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
}

/* Modern sidebar styling */
.admin-sidebar .postbox {
    margin-bottom: 24px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.admin-sidebar .inside {
    padding: 20px;
}

.admin-sidebar .inside p {
    margin: 0 0 12px 0;
    line-height: 1.6;
    color: #4a5568;
}

.admin-sidebar .inside ul {
    margin: 12px 0;
    padding-left: 24px;
}

.admin-sidebar .inside li {
    margin-bottom: 8px;
    line-height: 1.5;
    color: #4a5568;
}

.admin-sidebar .inside li a {
    color: #4285f4;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.admin-sidebar .inside li a:hover {
    color: #1a73e8;
    text-decoration: underline;
}

.admin-sidebar .inside h4 {
    margin: 20px 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    letter-spacing: -0.01em;
}

.admin-sidebar .inside h4:first-child {
    margin-top: 0;
}

/* Modern statistics table */
.admin-sidebar table.widefat {
    border: 1px solid #e2e8f0;
    border-collapse: collapse;
    width: 100%;
    margin: 12px 0;
    border-radius: 8px;
    overflow: hidden;
    background: #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.admin-sidebar table.widefat td {
    padding: 12px 16px;
    border-bottom: 1px solid #f1f5f9;
    font-size: 13px;
    color: #4a5568;
}

.admin-sidebar table.widefat tr:last-child td {
    border-bottom: none;
}

.admin-sidebar table.widefat tr:nth-child(even) {
    background: #f8fafc;
}

.admin-sidebar table.widefat td:first-child {
    font-weight: 500;
    color: #2d3748;
}

.admin-sidebar table.widefat td:last-child {
    text-align: right;
    font-weight: 600;
    color: #4285f4;
}

/* Modern button styling */
.openai-chatbot-admin .button {
    margin-right: 12px;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease;
    border: 2px solid transparent;
    cursor: pointer;
}

.openai-chatbot-admin .button-primary {
    background: linear-gradient(135deg, #4285f4 0%, #1a73e8 100%);
    border-color: transparent;
    color: #ffffff;
    box-shadow: 0 2px 8px rgba(66, 133, 244, 0.2);
}

.openai-chatbot-admin .button-primary:hover {
    background: linear-gradient(135deg, #1a73e8 0%, #1557b0 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
}

.openai-chatbot-admin .button-secondary {
    background: #ffffff;
    border-color: #e2e8f0;
    color: #4a5568;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.openai-chatbot-admin .button-secondary:hover {
    background: #f8fafc;
    border-color: #cbd5e0;
    color: #2d3748;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Modern notice styling */
.openai-chatbot-admin .notice {
    margin: 20px 0;
    padding: 16px 20px;
    border-left: 4px solid #4285f4;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border-radius: 0 8px 8px 0;
    border-right: 1px solid #e2e8f0;
    border-top: 1px solid #e2e8f0;
    border-bottom: 1px solid #e2e8f0;
}

.openai-chatbot-admin .notice-success {
    border-left-color: #10b981;
    background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
}

.openai-chatbot-admin .notice-warning {
    border-left-color: #f59e0b;
    background: linear-gradient(135deg, #ffffff 0%, #fffbeb 100%);
}

.openai-chatbot-admin .notice-error {
    border-left-color: #ef4444;
    background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
}

.openai-chatbot-admin .notice p {
    margin: 0.5em 0;
    padding: 0;
    color: #374151;
    line-height: 1.6;
}

.openai-chatbot-admin .notice code {
    display: block;
    margin: 12px 0;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 13px;
    line-height: 1.5;
    color: #4a5568;
}

/* API configuration warnings */
.openai-chatbot-admin .notice-warning strong {
    color: #b32d2e;
}

/* Responsive design */
@media (max-width: 1200px) {
    .openai-chatbot-admin {
        flex-direction: column;
    }
    
    .admin-sidebar {
        max-width: none;
    }
    
    .openai-chatbot-admin .form-table th {
        width: 150px;
    }
}

@media (max-width: 782px) {
    .openai-chatbot-admin .form-table th,
    .openai-chatbot-admin .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }
    
    .openai-chatbot-admin .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }
    
    .openai-chatbot-admin .form-table td {
        padding-top: 0;
        padding-bottom: 15px;
        border-bottom: 1px solid #e1e1e1;
    }
    
    .openai-chatbot-admin .form-table input[type="text"],
    .openai-chatbot-admin .form-table input[type="number"],
    .openai-chatbot-admin .form-table select,
    .openai-chatbot-admin .form-table textarea {
        max-width: 100%;
    }
}

/* Loading states */
.openai-chatbot-admin .loading {
    opacity: 0.6;
    pointer-events: none;
}

.openai-chatbot-admin .spinner {
    float: none;
    margin: 0 5px;
}

/* Accessibility improvements */
.openai-chatbot-admin .screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
}

.openai-chatbot-admin input:focus,
.openai-chatbot-admin select:focus,
.openai-chatbot-admin textarea:focus,
.openai-chatbot-admin button:focus {
    outline: 2px solid #2271b1;
    outline-offset: 1px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .openai-chatbot-admin .postbox {
        border-color: #000;
    }
    
    .openai-chatbot-admin .postbox-header {
        background: #fff;
        border-bottom-color: #000;
    }
    
    .openai-chatbot-admin .form-table td {
        border-bottom-color: #000;
    }
}

/* Print styles */
@media print {
    .openai-chatbot-admin .admin-sidebar {
        display: none;
    }
    
    .openai-chatbot-admin .button {
        display: none;
    }
    
    .openai-chatbot-admin .postbox {
        border: 1px solid #000;
        box-shadow: none;
        page-break-inside: avoid;
    }
}

/* Custom utility classes */
.openai-chatbot-admin .text-center {
    text-align: center;
}

.openai-chatbot-admin .text-right {
    text-align: right;
}

.openai-chatbot-admin .margin-top {
    margin-top: 20px;
}

.openai-chatbot-admin .margin-bottom {
    margin-bottom: 20px;
}

.openai-chatbot-admin .padding {
    padding: 15px;
}

.openai-chatbot-admin .no-margin {
    margin: 0;
}

.openai-chatbot-admin .no-padding {
    padding: 0;
}

/* Status indicators */
.openai-chatbot-admin .status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    vertical-align: middle;
}

.openai-chatbot-admin .status-indicator.connected {
    background: #00a32a;
}

.openai-chatbot-admin .status-indicator.disconnected {
    background: #d63638;
}

.openai-chatbot-admin .status-indicator.warning {
    background: #dba617;
}

/* Tabs (if needed for future expansion) */
.openai-chatbot-admin .nav-tab-wrapper {
    border-bottom: 1px solid #c3c4c7;
    margin: 0 0 20px 0;
    padding: 0;
}

.openai-chatbot-admin .nav-tab {
    background: #f1f1f1;
    border: 1px solid #c3c4c7;
    border-bottom: none;
    color: #2c3338;
    text-decoration: none;
    padding: 8px 12px;
    margin: 0 5px -1px 0;
    display: inline-block;
    font-size: 13px;
    line-height: 1.4;
}

.openai-chatbot-admin .nav-tab:hover {
    background: #fff;
    color: #2c3338;
}

.openai-chatbot-admin .nav-tab-active {
    background: #fff;
    border-bottom: 1px solid #fff;
    color: #000;
}

/* Animation for smooth transitions */
.openai-chatbot-admin .postbox,
.openai-chatbot-admin .notice {
    transition: all 0.3s ease;
}

.openai-chatbot-admin .postbox:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

/* Enhanced responsive design */
@media (max-width: 1200px) {
    .openai-chatbot-admin {
        flex-direction: column;
        gap: 20px;
    }

    .admin-sidebar {
        max-width: none;
    }

    .openai-chatbot-admin .form-table th {
        width: 180px;
    }

    .color-picker-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .color-picker-row label {
        min-width: auto;
        margin-bottom: 4px;
    }
}

@media (max-width: 782px) {
    .openai-chatbot-admin .form-table th,
    .openai-chatbot-admin .form-table td {
        display: block;
        width: 100%;
        padding: 12px 0;
    }

    .openai-chatbot-admin .form-table th {
        border-bottom: none;
        padding-bottom: 8px;
        font-size: 15px;
    }

    .openai-chatbot-admin .form-table td {
        padding-top: 0;
        padding-bottom: 20px;
        border-bottom: 2px solid #f1f5f9;
    }

    .openai-chatbot-admin .form-table input[type="text"],
    .openai-chatbot-admin .form-table input[type="number"],
    .openai-chatbot-admin .form-table select,
    .openai-chatbot-admin .form-table textarea {
        max-width: 100%;
    }

    .color-picker-section {
        padding: 16px;
    }

    .message-preview {
        padding: 16px;
    }

    .preview-content {
        max-width: 85%;
    }
}