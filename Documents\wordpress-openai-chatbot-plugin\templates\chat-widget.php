<?php
/**
 * Chat Widget Template
 * 
 * This template renders the main chat widget that appears on the frontend
 * It provides the HTML structure for the floating chat interface
 * 
 * @package OpenAI_Chatbot
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Clear settings cache to ensure fresh data
$plugin_instance = OpenAI_Chatbot_Plugin::get_instance();
if (method_exists($plugin_instance, 'clear_settings_cache')) {
    $plugin_instance->clear_settings_cache();
}

// Get plugin options (force fresh retrieval)
$widget_position = get_option('openai_chatbot_position', 'bottom-right');
$widget_theme = get_option('openai_chatbot_theme', 'default');
$welcome_message = get_option('openai_chatbot_welcome_message', __('Hello! How can I help you today?', 'openai-chatbot'));
$quick_questions = get_option('openai_chatbot_quick_questions', "What products do you offer?\nHow can I contact support?\nWhat are your business hours?");
$is_enabled = get_option('openai_chatbot_enabled', true);
$bot_name = get_option('openai_chatbot_bot_name', __('AI Assistant', 'openai-chatbot'));
$placeholder = get_option('openai_chatbot_placeholder', __('Type your message...', 'openai-chatbot'));
$powered_by_text = get_option('openai_chatbot_powered_by_text', __('Powered by OpenAI', 'openai-chatbot'));
$max_message_length = (int) get_option('openai_chatbot_max_message_length', 1000);
$show_timestamps = (bool) get_option('openai_chatbot_show_timestamps', false);



// Don't render if disabled
if (!$is_enabled) {
    return;
}

// Check if API key is configured (database first, then wp-config)
$api_key = get_option('openai_chatbot_api_key', '');
if (empty($api_key) && defined('OPENAI_API_KEY')) {
    $api_key = OPENAI_API_KEY;
}

$assistant_id = get_option('openai_chatbot_assistant_id', '');
if (empty($assistant_id) && defined('OPENAI_ASSISTANT_ID')) {
    $assistant_id = OPENAI_ASSISTANT_ID;
}

if (empty($api_key) || empty($assistant_id)) {
    // Only show to administrators
    if (current_user_can('manage_options')) {
        echo '<div style="position: fixed; bottom: 20px; right: 20px; background: #f44336; color: white; padding: 10px; border-radius: 5px; z-index: 999999; font-size: 12px;">';
        echo __('OpenAI Chatbot: API key or Assistant ID not configured', 'openai-chatbot');
        echo '</div>';
    }
    return;
}

// Widget classes
$widget_classes = array(
    'openai-chatbot-widget',
    'position-' . $widget_position,
    'theme-' . $widget_theme
);

// Add mobile class if needed
if (wp_is_mobile()) {
    $widget_classes[] = 'mobile-device';
}

$widget_class_string = implode(' ', $widget_classes);
?>

<div id="openai-chatbot" class="<?php echo esc_attr($widget_class_string); ?>" data-status="closed">
    <!-- Chat Toggle Button -->
    <div class="chatbot-toggle" role="button" tabindex="0" aria-label="<?php esc_attr_e('Open chat', 'openai-chatbot'); ?>">
        <div class="toggle-icon">
            <!-- Chat Icon (when closed) -->
            <svg class="chat-icon" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
            </svg>
            
            <!-- Close Icon (when open) -->
            <svg class="close-icon" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
        </div>
        
        <!-- Connection Status Indicator -->
        <div class="connection-indicator" aria-hidden="true">
            <span class="status-dot connected" title="<?php esc_attr_e('Connected', 'openai-chatbot'); ?>"></span>
        </div>
    </div>
    
    <!-- Chat Window -->
    <div class="chatbot-window" role="dialog" aria-labelledby="chatbot-title" aria-hidden="true">
        <!-- Chat Header -->
        <div class="chatbot-header">
            <div class="header-content">
                <div class="bot-avatar" aria-hidden="true">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                </div>
                <div class="bot-info">
                    <h3 id="chatbot-title" class="bot-name">
                        <?php echo esc_html($bot_name); ?>
                    </h3>
                    <p class="bot-status" aria-live="polite">
                        <?php esc_html_e('Online', 'openai-chatbot'); ?>
                    </p>
                </div>
            </div>
            <button class="minimize-btn" type="button" aria-label="<?php esc_attr_e('Close chat', 'openai-chatbot'); ?>">
                <svg viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                    <path d="M19 13H5v-2h14v2z"/>
                </svg>
            </button>
        </div>
        
        <!-- Messages Area -->
        <div class="chatbot-messages">
            <div class="messages-container" role="log" aria-live="polite" aria-label="<?php esc_attr_e('Chat messages', 'openai-chatbot'); ?>">
                <!-- Welcome Message -->
                <div class="welcome-message">
                    <div class="message bot-message" role="article">
                        <div class="message-avatar" aria-hidden="true">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        <div class="message-bubble">
                            <div class="message-content">
                                <p><?php echo wp_kses_post($welcome_message); ?></p>
                            </div>
                            <?php if ($show_timestamps): ?>
                                <div class="message-time" aria-label="<?php esc_attr_e('Message time', 'openai-chatbot'); ?>">
                                    <?php echo esc_html(current_time('H:i')); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Quick Questions -->
                <?php if (!empty($quick_questions)): ?>
                    <?php
                    $questions_array = array_filter(array_map('trim', explode("\n", $quick_questions)));
                    if (!empty($questions_array)):
                    ?>
                        <div class="quick-questions">
                            <div class="quick-questions-container">
                                <?php foreach ($questions_array as $index => $question): ?>
                                    <button type="button" class="quick-question-btn" data-question="<?php echo esc_attr($question); ?>">
                                        <?php echo esc_html($question); ?>
                                    </button>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
            
            <!-- Typing Indicator -->
            <div class="typing-indicator" style="display: none;" aria-hidden="true">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <span class="typing-text"><?php esc_html_e('AI is thinking...', 'openai-chatbot'); ?></span>
            </div>
        </div>
        
        <!-- Input Area -->
        <div class="chatbot-input">
            <form class="input-container" role="form" aria-label="<?php esc_attr_e('Send message', 'openai-chatbot'); ?>">
                <label for="chatbot-message-input" class="sr-only">
                    <?php esc_html_e('Type your message', 'openai-chatbot'); ?>
                </label>
                <textarea
                    id="chatbot-message-input"
                    class="message-input"
                    placeholder="<?php echo esc_attr($placeholder); ?>"
                    maxlength="<?php echo esc_attr($max_message_length); ?>"
                    rows="1"
                    aria-describedby="character-count"
                    autocomplete="off"
                    spellcheck="true"
                ></textarea>
                
                <button 
                    class="send-button" 
                    type="submit" 
                    disabled 
                    aria-label="<?php esc_attr_e('Send message', 'openai-chatbot'); ?>"
                >
                    <!-- Send Icon -->
                    <svg class="send-icon" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                    
                    <!-- Loading Icon -->
                    <svg class="loading-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;" aria-hidden="true">
                        <path d="M12 4V2A10 10 0 0 0 2 12h2a8 8 0 0 1 8-8z"/>
                    </svg>
                </button>
            </form>
            
            <!-- Input Footer -->
            <div class="input-footer">
                <div id="character-count" class="character-count" aria-live="polite">
                    <span class="current">0</span>/<span class="max"><?php echo esc_html($max_message_length); ?></span>
                </div>
                <div class="powered-by">
                    <?php echo esc_html($powered_by_text); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Add inline styles for positioning if needed
$custom_position = get_option('openai_chatbot_custom_position', '');
if (!empty($custom_position)) {
    echo '<style>';
    echo '.openai-chatbot-widget { ' . esc_html($custom_position) . ' }';
    echo '</style>';
}

// Add custom CSS if provided
$custom_css = get_option('openai_chatbot_custom_css', '');
if (!empty($custom_css)) {
    echo '<style>';
    echo wp_strip_all_tags($custom_css);
    echo '</style>';
}
?>

<!-- Screen Reader Announcements -->
<div id="chatbot-announcements" class="sr-only" aria-live="assertive" aria-atomic="true"></div>

<?php
// Add structured data for accessibility
if (get_option('openai_chatbot_add_structured_data', false)) {
    $structured_data = array(
        '@context' => 'https://schema.org',
        '@type' => 'SoftwareApplication',
        'name' => $bot_name,
        'applicationCategory' => 'ChatBot',
        'operatingSystem' => 'Web Browser',
        'description' => __('AI-powered chat assistant', 'openai-chatbot'),
        'provider' => array(
            '@type' => 'Organization',
            'name' => get_bloginfo('name')
        )
    );
    
    echo '<script type="application/ld+json">';
    echo wp_json_encode($structured_data);
    echo '</script>';
}
?>